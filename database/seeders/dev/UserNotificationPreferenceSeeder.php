<?php

declare(strict_types=1);

namespace Database\Seeders\Dev;

use App\Models\User\NotificationChannel;
use App\Models\User\NotificationType;
use App\Models\User\User;
use App\Models\User\UserNotificationPreference;
use Illuminate\Database\Seeder;

class UserNotificationPreferenceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting UserNotificationPreferenceSeeder...');

        // Check for required data
        if (! $this->hasRequiredData()) {
            return;
        }

        // Create notification preferences for all users
        $preferencesCreated = $this->createUserNotificationPreferences();

        $this->command->info("Created {$preferencesCreated} user notification preferences.");
        $this->command->info('UserNotificationPreferenceSeeder completed successfully.');
    }

    /**
     * Check if required data exists.
     */
    private function hasRequiredData(): bool
    {
        $usersCount = User::count();
        $channelsCount = NotificationChannel::count();
        $typesCount = NotificationType::count();

        if ($usersCount === 0) {
            $this->command->warn('No users found. Please run UserSeeder first.');

            return false;
        }

        if ($channelsCount === 0) {
            $this->command->warn('No notification channels found. Skipping UserNotificationPreferenceSeeder.');

            return false;
        }

        if ($typesCount === 0) {
            $this->command->warn('No notification types found. Skipping UserNotificationPreferenceSeeder.');

            return false;
        }

        return true;
    }

    /**
     * Create notification preferences for all users.
     */
    private function createUserNotificationPreferences(): int
    {
        $users = User::take(50)->get(); // Limit to 50 users for performance
        $channels = NotificationChannel::all();
        $types = NotificationType::all();
        $preferencesCreated = 0;

        foreach ($users as $user) {
            foreach ($types as $type) {
                foreach ($channels as $channel) {
                    // Create preference for each user-type-channel combination
                    $isEnabled = $this->shouldEnableNotification($user, $type, $channel);

                    UserNotificationPreference::create([
                        'user_id' => $user->id,
                        'tenant_id' => $user->tenant_id,
                        'notification_type' => $type->name,
                        'channel' => $channel->channel_type->value,
                        'is_enabled' => $isEnabled,
                        'created_at' => fake()->dateTimeBetween($user->created_at, 'now'),
                        'updated_at' => fake()->dateTimeBetween($user->created_at, 'now'),
                    ]);

                    $preferencesCreated++;
                }
            }
        }

        return $preferencesCreated;
    }

    /**
     * Determine if a notification should be enabled based on user, type, and channel.
     */
    private function shouldEnableNotification(User $user, NotificationType $type, NotificationChannel $channel): bool
    {
        // Get user roles to determine preferences
        $userRoles = $user->getRoles();
        $isBusinessUser = $userRoles->contains('name', 'business-owner') || $userRoles->contains('name', 'business-manager');
        $isDeliveryProvider = $userRoles->contains('name', 'delivery-partner');
        $isCustomer = $userRoles->contains('name', 'customer');

        // Default preferences based on notification type and channel
        $preferences = $this->getDefaultPreferences();

        // Get the preference key
        $preferenceKey = $type->name.'_'.$channel->channel_type->value;

        // Check if we have a specific preference
        if (isset($preferences[$preferenceKey])) {
            $basePreference = $preferences[$preferenceKey];
        } else {
            // Fallback to type-based preferences
            $basePreference = $preferences[$type->name] ?? 0.7; // Default 70% chance
        }

        // Adjust based on user type
        if ($isBusinessUser) {
            $basePreference = $this->adjustForBusinessUser($basePreference, $type, $channel);
        } elseif ($isDeliveryProvider) {
            $basePreference = $this->adjustForDeliveryProvider($basePreference, $type, $channel);
        } elseif ($isCustomer) {
            $basePreference = $this->adjustForCustomer($basePreference, $type, $channel);
        }

        // Add some randomness
        $randomFactor = fake()->randomFloat(2, -0.2, 0.2);
        $finalPreference = max(0, min(1, $basePreference + $randomFactor));

        return fake()->boolean((int) ($finalPreference * 100));
    }

    /**
     * Get default notification preferences.
     */
    private function getDefaultPreferences(): array
    {
        return [
            // High priority notifications (usually enabled)
            'order_confirmed' => 0.95,
            'order_cancelled' => 0.90,
            'payment_successful' => 0.95,
            'payment_failed' => 0.95,
            'delivery_assigned' => 0.90,
            'delivery_completed' => 0.95,
            'security_alert' => 0.98,

            // Medium priority notifications
            'order_preparing' => 0.75,
            'order_ready' => 0.85,
            'delivery_picked_up' => 0.80,
            'delivery_in_transit' => 0.70,
            'new_order' => 0.90,
            'welcome' => 0.85,

            // Lower priority notifications
            'promotion_available' => 0.40,
            'system_maintenance' => 0.60,
            'daily_summary' => 0.50,

            // Channel-specific adjustments
            'email' => 0.80,
            'sms' => 0.60,
            'push' => 0.85,
            'in_app' => 0.90,
        ];
    }

    /**
     * Adjust preferences for business users.
     */
    private function adjustForBusinessUser(float $basePreference, NotificationType $type, NotificationChannel $channel): float
    {
        // Business users prefer business-related notifications
        $businessTypes = ['order_confirmed', 'order_cancelled', 'payment_successful', 'new_order', 'low_stock_alert'];

        if (in_array($type->name, $businessTypes)) {
            $basePreference += 0.15;
        }

        // Business users prefer email and in-app notifications
        if (in_array($channel->channel_type->value, ['email', 'in_app'])) {
            $basePreference += 0.10;
        }

        // Less interested in promotional content
        if (in_array($type->name, ['promotion_available'])) {
            $basePreference -= 0.20;
        }

        return $basePreference;
    }

    /**
     * Adjust preferences for delivery providers.
     */
    private function adjustForDeliveryProvider(float $basePreference, NotificationType $type, NotificationChannel $channel): float
    {
        // Delivery providers prefer delivery-related notifications
        $deliveryTypes = ['delivery_assigned', 'delivery_picked_up', 'delivery_in_transit', 'delivery_completed'];

        if (in_array($type->name, $deliveryTypes)) {
            $basePreference += 0.20;
        }

        // Delivery providers prefer SMS and push notifications (mobile-first)
        if (in_array($channel->channel_type->value, ['sms', 'push'])) {
            $basePreference += 0.15;
        }

        // Less interested in promotional content
        if (in_array($type->name, ['promotion_available'])) {
            $basePreference -= 0.25;
        }

        return $basePreference;
    }

    /**
     * Adjust preferences for customers.
     */
    private function adjustForCustomer(float $basePreference, NotificationType $type, NotificationChannel $channel): float
    {
        // Customers prefer order and delivery updates
        $customerTypes = ['order_confirmed', 'order_preparing', 'order_ready', 'delivery_in_transit', 'delivery_completed'];

        if (in_array($type->name, $customerTypes)) {
            $basePreference += 0.10;
        }

        // Customers prefer push and in-app notifications
        if (in_array($channel->channel_type->value, ['push', 'in_app'])) {
            $basePreference += 0.10;
        }

        // Some customers like promotional offers
        if ($type->name === 'promotion_available') {
            $basePreference += 0.15;
        }

        return $basePreference;
    }
}
