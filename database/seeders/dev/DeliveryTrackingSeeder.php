<?php

declare(strict_types=1);

namespace Database\Seeders\Dev;

use App\Models\Delivery\CustomerTrackingSession;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\DeliveryEtaPrediction;
use App\Models\Delivery\DeliveryRoute;
use App\Models\Delivery\DeliveryStatusUpdate;
use App\Models\Delivery\Order;
use App\Models\Delivery\UserDelivery;
use App\Models\User\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DeliveryTrackingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (! $this->hasRequiredData()) {
            return;
        }

        $this->command->info('Seeding delivery tracking data...');

        $this->seedDeliveryRoutes();
        $this->seedDeliveryStatusUpdates();
        $this->seedDeliveryEtaPredictions();
        $this->seedCustomerTrackingSessions();

        $this->command->info('Delivery tracking data seeded successfully.');
    }

    /**
     * Seed delivery routes.
     */
    private function seedDeliveryRoutes(): void
    {
        $deliveries = Delivery::with('deliveryProvider')->take(25)->get();

        // Use direct DB query to get delivery driver/partner users
        $driverIds = DB::table('users')
            ->join('assigned_roles', DB::raw('CAST(users.id AS TEXT)'), '=', 'assigned_roles.entity_id')
            ->join('roles', 'assigned_roles.role_id', '=', 'roles.id')
            ->whereIn('roles.name', ['delivery-partner', 'delivery-driver', 'delivery-rider'])
            ->where('assigned_roles.entity_type', 'App\\Models\\User')
            ->limit(15)
            ->pluck('users.id');

        $drivers = User::whereIn('id', $driverIds)->get();

        if ($drivers->isEmpty()) {
            $this->command->warn('No delivery drivers found. Skipping delivery routes seeding.');

            return;
        }

        foreach ($deliveries as $delivery) {
            $driver = $drivers->random();

            // Lagos coordinates for realistic routes
            $pickupLat = 6.5244 + (fake()->randomFloat(4, -0.1, 0.1));
            $pickupLng = 3.3792 + (fake()->randomFloat(4, -0.1, 0.1));
            $deliveryLat = 6.5244 + (fake()->randomFloat(4, -0.2, 0.2));
            $deliveryLng = 3.3792 + (fake()->randomFloat(4, -0.2, 0.2));

            $plannedRoute = [
                'pickup_location' => ['lat' => $pickupLat, 'lng' => $pickupLng],
                'delivery_location' => ['lat' => $deliveryLat, 'lng' => $deliveryLng],
                'waypoints' => [
                    ['lat' => $pickupLat + 0.01, 'lng' => $pickupLng + 0.01],
                    ['lat' => $deliveryLat - 0.01, 'lng' => $deliveryLng - 0.01],
                ],
                'route_instructions' => [
                    'Head north on Lagos-Ibadan Expressway',
                    'Turn right onto Ikorodu Road',
                    'Continue straight for 2.5km',
                    'Turn left at destination',
                ],
            ];

            $actualRoute = fake()->boolean(80) ? [
                'pickup_location' => ['lat' => $pickupLat, 'lng' => $pickupLng],
                'delivery_location' => ['lat' => $deliveryLat, 'lng' => $deliveryLng],
                'actual_path' => [
                    ['lat' => $pickupLat, 'lng' => $pickupLng, 'timestamp' => now()->subHour()],
                    ['lat' => $pickupLat + 0.005, 'lng' => $pickupLng + 0.008, 'timestamp' => now()->subMinutes(45)],
                    ['lat' => $deliveryLat - 0.003, 'lng' => $deliveryLng - 0.005, 'timestamp' => now()->subMinutes(15)],
                    ['lat' => $deliveryLat, 'lng' => $deliveryLng, 'timestamp' => now()->subMinutes(5)],
                ],
            ] : null;

            $plannedDistance = fake()->randomFloat(2, 5.5, 25.0);
            $actualDistance = $actualRoute ? $plannedDistance + fake()->randomFloat(2, -2.0, 3.0) : null;

            DeliveryRoute::create([
                'tenant_id' => $delivery->tenant_id,
                'delivery_id' => $delivery->id,
                'driver_id' => $driver->id,
                'planned_route' => $plannedRoute,
                'actual_route' => $actualRoute,
                'planned_distance_km' => $plannedDistance,
                'actual_distance_km' => $actualDistance,
                'planned_duration_minutes' => fake()->numberBetween(25, 60),
                'actual_duration_minutes' => $actualRoute ? fake()->numberBetween(20, 75) : null,
                'traffic_conditions' => [
                    'average_speed' => fake()->numberBetween(15, 45).' km/h',
                    'traffic_level' => fake()->randomElement(['light', 'moderate', 'heavy']),
                    'weather' => fake()->randomElement(['clear', 'rainy', 'cloudy']),
                    'road_conditions' => fake()->randomElement(['good', 'fair', 'poor']),
                ],
                'route_deviations' => fake()->boolean(30) ? [
                    'deviation_reason' => fake()->randomElement(['traffic_jam', 'road_closure', 'customer_request']),
                    'additional_distance' => fake()->randomFloat(2, 0.5, 3.0),
                    'additional_time' => fake()->numberBetween(5, 20),
                ] : null,
                'route_started_at' => $delivery->created_at->addMinutes(fake()->numberBetween(10, 30)),
                'route_completed_at' => fake()->boolean(70) ? $delivery->created_at->addMinutes(fake()->numberBetween(40, 90)) : null,
            ]);
        }
    }

    /**
     * Seed delivery status updates.
     */
    private function seedDeliveryStatusUpdates(): void
    {
        $deliveries = Delivery::take(30)->get();
        $users = User::take(20)->get();

        $statusFlow = [
            'pending' => 'confirmed',
            'confirmed' => 'preparing',
            'preparing' => 'ready_for_pickup',
            'ready_for_pickup' => 'picked_up',
            'picked_up' => 'in_transit',
            'in_transit' => 'delivered',
        ];

        foreach ($deliveries as $delivery) {
            $currentStatus = 'pending';
            $statusTime = $delivery->created_at;

            foreach ($statusFlow as $newStatus) {
                $statusTime = $statusTime->addMinutes(fake()->numberBetween(5, 25));

                DeliveryStatusUpdate::create([
                    'tenant_id' => $delivery->tenant_id,
                    'delivery_id' => $delivery->id,
                    'updated_by_user_id' => $users->random()->id,
                    'previous_status' => $currentStatus === 'pending' ? null : $currentStatus,
                    'new_status' => $newStatus,
                    'status_reason' => $this->getStatusReason($newStatus),
                    'location_data' => $this->getLocationData($newStatus),
                    'metadata' => [
                        'update_source' => fake()->randomElement(['driver_app', 'business_dashboard', 'system_auto']),
                        'device_info' => fake()->randomElement(['Android', 'iOS', 'Web']),
                    ],
                    'customer_notified' => fake()->boolean(85),
                    'business_notified' => fake()->boolean(90),
                    'status_changed_at' => $statusTime,
                ]);

                $currentStatus = $newStatus;

                // Don't always complete the full flow
                if (fake()->boolean(15) && $newStatus !== 'delivered') {
                    break;
                }
            }
        }
    }

    /**
     * Seed delivery ETA predictions.
     */
    private function seedDeliveryEtaPredictions(): void
    {
        $deliveries = Delivery::take(20)->get();

        foreach ($deliveries as $delivery) {
            // Create multiple ETA predictions as delivery progresses
            $predictionCount = fake()->numberBetween(2, 5);

            for ($i = 0; $i < $predictionCount; $i++) {
                $predictedAt = $delivery->created_at->addMinutes($i * 15);
                $estimatedPickup = $predictedAt->addMinutes(fake()->numberBetween(10, 30));
                $estimatedDelivery = $estimatedPickup->addMinutes(fake()->numberBetween(20, 45));

                DeliveryEtaPrediction::create([
                    'tenant_id' => $delivery->tenant_id,
                    'delivery_id' => $delivery->id,
                    'predicted_at' => $predictedAt,
                    'estimated_pickup_time' => $estimatedPickup,
                    'estimated_delivery_time' => $estimatedDelivery,
                    'confidence_percentage' => fake()->numberBetween(75, 95),
                    'prediction_factors' => [
                        'traffic_conditions' => fake()->randomElement(['light', 'moderate', 'heavy']),
                        'weather' => fake()->randomElement(['clear', 'rainy', 'cloudy']),
                        'driver_performance' => fake()->randomFloat(2, 3.5, 5.0),
                        'historical_data' => 'last_30_deliveries',
                        'distance_km' => fake()->randomFloat(2, 5.0, 20.0),
                    ],
                    'prediction_model' => fake()->randomElement(['ml_model_v2', 'statistical_model', 'hybrid_model']),
                    'accuracy_score' => $i > 0 ? fake()->randomFloat(4, 0.7500, 0.9500) : null,
                    'is_active' => $i === ($predictionCount - 1), // Last prediction is active
                ]);
            }
        }
    }

    /**
     * Seed customer tracking sessions.
     */
    private function seedCustomerTrackingSessions(): void
    {
        $orders = Order::take(25)->get();
        $userDeliveries = UserDelivery::take(15)->get();

        // Use direct DB query to get customer users
        $customerIds = DB::table('users')
            ->join('assigned_roles', DB::raw('CAST(users.id AS TEXT)'), '=', 'assigned_roles.entity_id')
            ->join('roles', 'assigned_roles.role_id', '=', 'roles.id')
            ->where('roles.name', 'customer')
            ->where('assigned_roles.entity_type', 'App\\Models\\User\\User')
            ->limit(30)
            ->pluck('users.id');

        $customers = User::whereIn('id', $customerIds)->get();

        if ($customers->isEmpty()) {
            $this->command->warn('No customers found. Skipping customer tracking sessions seeding.');

            return;
        }

        // Sessions for orders
        foreach ($orders as $order) {
            $customer = $customers->random();
            $sessionStart = $order->created_at->addMinutes(fake()->numberBetween(5, 30));

            // Ensure session start is not in the future
            if ($sessionStart > now()) {
                $sessionStart = now()->subMinutes(fake()->numberBetween(5, 60));
            }

            CustomerTrackingSession::create([
                'order_id' => $order->id,
                'user_delivery_id' => null,
                'customer_id' => $customer->id,
                'tracking_token' => 'TRK'.strtoupper(fake()->bothify('??##??##')),
                'session_started_at' => $sessionStart,
                'last_accessed_at' => fake()->dateTimeBetween($sessionStart, 'now'),
                'session_expires_at' => $sessionStart->copy()->addHours(24),
                'access_count' => fake()->numberBetween(1, 15),
                'access_log' => [
                    ['timestamp' => $sessionStart, 'ip' => fake()->ipv4(), 'user_agent' => 'Mobile App'],
                    ['timestamp' => $sessionStart->copy()->addMinutes(10), 'ip' => fake()->ipv4(), 'user_agent' => 'Mobile App'],
                ],
                'is_active' => fake()->boolean(70),
            ]);
        }

        // Sessions for user deliveries
        foreach ($userDeliveries as $userDelivery) {
            $customer = $customers->random();
            $sessionStart = $userDelivery->created_at->addMinutes(fake()->numberBetween(2, 15));

            // Ensure session start is not in the future
            if ($sessionStart > now()) {
                $sessionStart = now()->subMinutes(fake()->numberBetween(5, 60));
            }

            CustomerTrackingSession::create([
                'order_id' => null,
                'user_delivery_id' => $userDelivery->id,
                'customer_id' => $customer->id,
                'tracking_token' => 'TRK'.strtoupper(fake()->bothify('??##??##')),
                'session_started_at' => $sessionStart,
                'last_accessed_at' => fake()->dateTimeBetween($sessionStart, 'now'),
                'session_expires_at' => $sessionStart->copy()->addHours(12),
                'access_count' => fake()->numberBetween(1, 8),
                'access_log' => [
                    ['timestamp' => $sessionStart, 'ip' => fake()->ipv4(), 'user_agent' => 'Web Browser'],
                ],
                'is_active' => fake()->boolean(60),
            ]);
        }
    }

    /**
     * Get status reason based on new status.
     */
    private function getStatusReason(string $status): ?string
    {
        return match ($status) {
            'confirmed' => 'Order confirmed by business',
            'preparing' => 'Kitchen started preparing order',
            'ready_for_pickup' => 'Order ready for driver pickup',
            'picked_up' => 'Driver collected order from business',
            'in_transit' => 'Driver en route to customer',
            'delivered' => 'Order successfully delivered to customer',
            default => null,
        };
    }

    /**
     * Get location data based on status.
     */
    private function getLocationData(string $status): ?array
    {
        if (in_array($status, ['picked_up', 'in_transit', 'delivered'])) {
            return [
                'lat' => 6.5244 + fake()->randomFloat(4, -0.1, 0.1),
                'lng' => 3.3792 + fake()->randomFloat(4, -0.1, 0.1),
                'accuracy' => fake()->numberBetween(5, 20),
                'timestamp' => now()->timestamp,
            ];
        }

        return null;
    }

    /**
     * Check if required data exists.
     */
    private function hasRequiredData(): bool
    {
        $deliveryCount = Delivery::count();
        $orderCount = Order::count();

        if ($deliveryCount === 0) {
            $this->command->warn('No deliveries found. Please run DeliverySeeder first.');

            return false;
        }

        if ($orderCount === 0) {
            $this->command->warn('No orders found. Please run OrderSeeder first.');

            return false;
        }

        return true;
    }
}
