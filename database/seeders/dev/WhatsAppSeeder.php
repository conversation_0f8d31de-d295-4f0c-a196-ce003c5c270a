<?php

declare(strict_types=1);

namespace Database\Seeders\Dev;

use App\Enums\System\WhatsAppMessageType;
use App\Enums\System\WhatsAppTemplateCategory;
use App\Models\Business\Business;
use App\Models\Core\WhatsAppConversation;
use App\Models\Core\WhatsAppMessage;
use App\Models\Core\WhatsAppTemplate;
use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WhatsAppSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (! $this->hasRequiredData()) {
            return;
        }

        $this->command->info('Seeding WhatsApp data...');

        $this->seedTemplates();
        $this->seedConversations();

        $this->command->info('WhatsApp data seeded successfully.');
    }

    /**
     * Seed WhatsApp templates.
     */
    private function seedTemplates(): void
    {
        $tenants = Tenant::take(5)->get();
        $businesses = Business::take(10)->get();

        // Platform-wide templates
        $platformTemplates = [
            [
                'template_name' => 'order_confirmation',
                'category' => WhatsAppTemplateCategory::UTILITY,
                'template_components' => [
                    'header' => ['type' => 'text', 'text' => 'Order Confirmed! 🎉'],
                    'body' => [
                        'text' => 'Hi {{1}}, your order #{{2}} has been confirmed. Total: ₦{{3}}. Estimated delivery: {{4}}.',
                    ],
                    'footer' => ['text' => 'DeliveryNexus - Fast & Reliable'],
                    'buttons' => [
                        ['type' => 'url', 'text' => 'Track Order', 'url' => 'https://deliverynexus.com/track/{{5}}'],
                    ],
                ],
                'status' => 'approved',
            ],
            [
                'template_name' => 'delivery_update',
                'category' => WhatsAppTemplateCategory::UTILITY,
                'template_components' => [
                    'header' => ['type' => 'text', 'text' => 'Delivery Update 🚚'],
                    'body' => [
                        'text' => 'Your order #{{1}} is {{2}}. Driver: {{3}}. ETA: {{4}} minutes.',
                    ],
                    'footer' => ['text' => 'DeliveryNexus'],
                    'buttons' => [
                        ['type' => 'phone_number', 'text' => 'Call Driver', 'phone_number' => '{{5}}'],
                    ],
                ],
                'status' => 'approved',
            ],
            [
                'template_name' => 'payment_reminder',
                'category' => WhatsAppTemplateCategory::UTILITY,
                'template_components' => [
                    'header' => ['type' => 'text', 'text' => 'Payment Reminder 💳'],
                    'body' => [
                        'text' => 'Hi {{1}}, your order #{{2}} is ready for delivery. Please complete payment of ₦{{3}}.',
                    ],
                    'buttons' => [
                        ['type' => 'url', 'text' => 'Pay Now', 'url' => 'https://deliverynexus.com/pay/{{4}}'],
                    ],
                ],
                'status' => 'approved',
            ],
        ];

        foreach ($platformTemplates as $template) {
            WhatsAppTemplate::create([
                'tenant_id' => null,
                'business_id' => null,
                'template_name' => $template['template_name'],
                'language_code' => 'en',
                'category' => $template['category'],
                'status' => $template['status'],
                'template_components' => $template['template_components'],
                'approved_at' => now(),
            ]);
        }

        // Business-specific templates
        foreach ($businesses->take(5) as $business) {
            WhatsAppTemplate::create([
                'tenant_id' => $business->tenant_id,
                'business_id' => $business->id,
                'template_name' => 'welcome_message',
                'language_code' => 'en',
                'category' => WhatsAppTemplateCategory::MARKETING,
                'status' => 'approved',
                'template_components' => [
                    'header' => ['type' => 'text', 'text' => "Welcome to {$business->name}! 👋"],
                    'body' => [
                        'text' => 'Hi {{1}}! Thanks for choosing us. Browse our menu and place your order easily via WhatsApp.',
                    ],
                    'footer' => ['text' => $business->name],
                    'buttons' => [
                        ['type' => 'quick_reply', 'text' => 'View Menu'],
                        ['type' => 'quick_reply', 'text' => 'Contact Support'],
                    ],
                ],
                'approved_at' => now(),
            ]);
        }
    }

    /**
     * Seed WhatsApp conversations and messages.
     */
    private function seedConversations(): void
    {
        $businesses = Business::take(8)->get();

        // Use direct DB query to get customer users
        $customerIds = DB::table('users')
            ->join('assigned_roles', DB::raw('CAST(users.id AS TEXT)'), '=', 'assigned_roles.entity_id')
            ->join('roles', 'assigned_roles.role_id', '=', 'roles.id')
            ->where('roles.name', 'customer')
            ->where('assigned_roles.entity_type', 'App\\Models\\User\\User')
            ->limit(15)
            ->pluck('users.id');

        $customers = User::whereIn('id', $customerIds)->get();

        $nigerianPhones = [
            '+2348012345678', '+2347098765432', '+2348123456789',
            '+2347087654321', '+2348034567890', '+2347076543210',
            '+2348045678901', '+2347065432109', '+2348056789012',
            '+2347054321098', '+2348067890123', '+2347043210987',
        ];

        foreach ($businesses as $index => $business) {
            // Create 2-3 conversations per business
            for ($i = 0; $i < fake()->numberBetween(2, 3); $i++) {
                $customer = $customers->random();
                $phone = $nigerianPhones[($index * 3 + $i) % count($nigerianPhones)];

                $conversation = WhatsAppConversation::create([
                    'tenant_id' => $business->tenant_id,
                    'business_id' => $business->id,
                    'customer_phone' => $phone,
                    'customer_user_id' => fake()->boolean(70) ? $customer->id : null,
                    'conversation_status' => fake()->randomElement(['active', 'completed', 'abandoned']),
                    'current_step' => fake()->randomElement(['greeting', 'menu_selection', 'order_details', 'payment', 'completed']),
                    'conversation_data' => [
                        'cart_items' => [],
                        'delivery_address' => fake()->address(),
                        'payment_method' => 'cash_on_delivery',
                    ],
                    'started_at' => fake()->dateTimeBetween('-7 days', 'now'),
                    'last_activity_at' => fake()->dateTimeBetween('-2 hours', 'now'),
                    'completed_at' => fake()->boolean(60) ? fake()->dateTimeBetween('-1 day', 'now') : null,
                ]);

                // Add messages to conversation
                $this->seedMessagesForConversation($conversation);
            }
        }
    }

    /**
     * Seed messages for a conversation.
     */
    private function seedMessagesForConversation(WhatsAppConversation $conversation): void
    {
        $messageCount = fake()->numberBetween(3, 12);
        $messageTemplates = [
            ['direction' => 'inbound', 'content' => 'Hi, I want to order food'],
            ['direction' => 'outbound', 'content' => 'Hello! Welcome to '.$conversation->business->name.'. Here\'s our menu:'],
            ['direction' => 'inbound', 'content' => 'I want jollof rice with chicken'],
            ['direction' => 'outbound', 'content' => 'Great choice! Jollof rice with chicken is ₦2,000. Anything else?'],
            ['direction' => 'inbound', 'content' => 'Add one coke please'],
            ['direction' => 'outbound', 'content' => 'Added! Your order: Jollof rice with chicken (₦2,000) + Coke (₦300) = ₦2,300. Delivery address?'],
            ['direction' => 'inbound', 'content' => 'Victoria Island, Lagos'],
            ['direction' => 'outbound', 'content' => 'Perfect! Delivery fee is ₦500. Total: ₦2,800. Confirm order?'],
            ['direction' => 'inbound', 'content' => 'Yes, confirm'],
            ['direction' => 'outbound', 'content' => 'Order confirmed! Your order #DN12345 will be delivered in 30-45 minutes.'],
        ];

        for ($i = 0; $i < min($messageCount, count($messageTemplates)); $i++) {
            $template = $messageTemplates[$i];
            $sentAt = $conversation->started_at->addMinutes($i * 2);

            WhatsAppMessage::create([
                'conversation_id' => $conversation->id,
                'message_id' => 'wamid.'.fake()->uuid(),
                'direction' => $template['direction'],
                'message_type' => WhatsAppMessageType::TEXT,
                'content' => $template['content'],
                'media_url' => null,
                'metadata' => [
                    'timestamp' => $sentAt->timestamp,
                    'from' => $template['direction'] === 'inbound' ? $conversation->customer_phone : 'business',
                ],
                'sent_at' => $sentAt,
                'delivered_at' => $sentAt->addSeconds(fake()->numberBetween(1, 10)),
                'read_at' => fake()->boolean(80) ? $sentAt->addSeconds(fake()->numberBetween(10, 300)) : null,
            ]);
        }
    }

    /**
     * Check if required data exists.
     */
    private function hasRequiredData(): bool
    {
        $businessCount = Business::count();

        // Use direct DB query to avoid UUID/string type issues with Bouncer
        $customerCount = DB::table('users')
            ->join('assigned_roles', DB::raw('CAST(users.id AS TEXT)'), '=', 'assigned_roles.entity_id')
            ->join('roles', 'assigned_roles.role_id', '=', 'roles.id')
            ->where('roles.name', 'customer')
            ->where('assigned_roles.entity_type', 'App\\Models\\User\\User')
            ->count();

        if ($businessCount === 0) {
            $this->command->warn('No businesses found. Please run BusinessSeeder first.');

            return false;
        }

        if ($customerCount === 0) {
            $this->command->warn('No customer users found. Please run UserSeeder first.');

            return false;
        }

        return true;
    }
}
