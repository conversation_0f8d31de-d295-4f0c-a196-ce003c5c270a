<?php

declare(strict_types=1);

namespace Database\Seeders\Prod;

use App\Enums\System\NotificationChannel;
use App\Models\User\NotificationType;
use Illuminate\Database\Seeder;

class NotificationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding notification types...');

        $types = [
            // Order-related notifications (High Priority)
            [
                'name' => 'order_confirmed',
                'description' => 'Order has been confirmed by the business',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::EMAIL->value],
                'priority' => 3,
                'category' => 'order',
                'template' => [
                    'title' => 'Order Confirmed',
                    'message' => 'Your order #{order_number} has been confirmed and is being prepared.',
                    'variables' => ['order_number', 'business_name', 'estimated_time'],
                ],
            ],
            [
                'name' => 'order_preparing',
                'description' => 'Order is being prepared',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value],
                'priority' => 2,
                'category' => 'order',
                'template' => [
                    'title' => 'Order Being Prepared',
                    'message' => 'Your order is now being prepared by {business_name}.',
                    'variables' => ['business_name', 'estimated_time'],
                ],
            ],
            [
                'name' => 'order_ready',
                'description' => 'Order is ready for pickup or delivery',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::SMS->value],
                'priority' => 3,
                'category' => 'order',
                'template' => [
                    'title' => 'Order Ready',
                    'message' => 'Your order is ready! A driver will pick it up shortly.',
                    'variables' => ['order_number', 'business_name'],
                ],
            ],
            [
                'name' => 'order_cancelled',
                'description' => 'Order has been cancelled',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::EMAIL->value, NotificationChannel::SMS->value],
                'priority' => 4,
                'category' => 'order',
                'template' => [
                    'title' => 'Order Cancelled',
                    'message' => 'Your order #{order_number} has been cancelled. You will receive a full refund.',
                    'variables' => ['order_number', 'cancellation_reason', 'refund_amount'],
                ],
            ],

            // Delivery-related notifications
            [
                'name' => 'delivery_assigned',
                'description' => 'Delivery has been assigned to a driver',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value],
                'priority' => 2,
                'category' => 'delivery',
                'template' => [
                    'title' => 'Driver Assigned',
                    'message' => '{driver_name} is on the way to pick up your order.',
                    'variables' => ['driver_name', 'driver_phone', 'estimated_pickup_time'],
                ],
            ],
            [
                'name' => 'delivery_picked_up',
                'description' => 'Order has been picked up by driver',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value],
                'priority' => 3,
                'category' => 'delivery',
                'template' => [
                    'title' => 'Order Picked Up',
                    'message' => 'Your order has been picked up and is on the way to you!',
                    'variables' => ['driver_name', 'estimated_delivery_time'],
                ],
            ],
            [
                'name' => 'delivery_in_transit',
                'description' => 'Order is in transit to customer',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value],
                'priority' => 2,
                'category' => 'delivery',
                'template' => [
                    'title' => 'Order In Transit',
                    'message' => 'Your order is on the way! Estimated arrival: {estimated_time}',
                    'variables' => ['estimated_time', 'driver_location'],
                ],
            ],
            [
                'name' => 'delivery_completed',
                'description' => 'Order has been delivered successfully',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::SMS->value],
                'priority' => 3,
                'category' => 'delivery',
                'template' => [
                    'title' => 'Order Delivered',
                    'message' => 'Your order has been delivered! Enjoy your meal.',
                    'variables' => ['delivery_time', 'driver_name'],
                ],
            ],

            // Payment-related notifications
            [
                'name' => 'payment_successful',
                'description' => 'Payment has been processed successfully',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::EMAIL->value],
                'priority' => 3,
                'category' => 'payment',
                'template' => [
                    'title' => 'Payment Successful',
                    'message' => 'Your payment of ₦{amount} has been processed successfully.',
                    'variables' => ['amount', 'payment_method', 'transaction_id'],
                ],
            ],
            [
                'name' => 'payment_failed',
                'description' => 'Payment processing failed',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::EMAIL->value],
                'priority' => 4,
                'category' => 'payment',
                'template' => [
                    'title' => 'Payment Failed',
                    'message' => 'Your payment could not be processed. Please try again or use a different payment method.',
                    'variables' => ['failure_reason', 'order_number'],
                ],
            ],

            // Business notifications
            [
                'name' => 'new_order',
                'description' => 'New order received by business',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::SMS->value],
                'priority' => 4,
                'category' => 'business',
                'template' => [
                    'title' => 'New Order Received',
                    'message' => 'You have a new order #{order_number} worth ₦{amount}',
                    'variables' => ['order_number', 'amount', 'customer_name', 'items_count'],
                ],
            ],
            [
                'name' => 'low_stock_alert',
                'description' => 'Product stock is running low',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::EMAIL->value],
                'priority' => 2,
                'category' => 'business',
                'template' => [
                    'title' => 'Low Stock Alert',
                    'message' => '{product_count} products are running low on stock.',
                    'variables' => ['product_count', 'product_names'],
                ],
            ],
            [
                'name' => 'daily_summary',
                'description' => 'Daily business performance summary',
                'available_channels' => [NotificationChannel::EMAIL->value],
                'priority' => 1,
                'category' => 'business',
                'template' => [
                    'title' => 'Daily Sales Summary',
                    'message' => 'Your daily sales report is ready with {order_count} orders and ₦{revenue} revenue.',
                    'variables' => ['order_count', 'revenue', 'date'],
                ],
            ],

            // System notifications
            [
                'name' => 'welcome',
                'description' => 'Welcome message for new users',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::EMAIL->value],
                'priority' => 2,
                'category' => 'system',
                'template' => [
                    'title' => 'Welcome to DeliveryNexus!',
                    'message' => 'Welcome to DeliveryNexus! Start exploring amazing businesses and services near you.',
                    'variables' => ['user_name'],
                ],
            ],
            [
                'name' => 'system_maintenance',
                'description' => 'System maintenance notifications',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::EMAIL->value],
                'priority' => 2,
                'category' => 'system',
                'template' => [
                    'title' => 'Scheduled Maintenance',
                    'message' => 'We will be performing maintenance from {start_time} to {end_time}.',
                    'variables' => ['start_time', 'end_time', 'duration'],
                ],
            ],
            [
                'name' => 'security_alert',
                'description' => 'Security-related alerts',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::EMAIL->value, NotificationChannel::SMS->value],
                'priority' => 4,
                'category' => 'security',
                'template' => [
                    'title' => 'Security Alert',
                    'message' => 'Unusual activity detected on your account. Please review your recent activity.',
                    'variables' => ['activity_type', 'location', 'time'],
                ],
            ],

            // Promotional notifications
            [
                'name' => 'promotion_available',
                'description' => 'Promotional offers and discounts',
                'available_channels' => [NotificationChannel::IN_APP->value, NotificationChannel::PUSH->value, NotificationChannel::EMAIL->value],
                'priority' => 1,
                'category' => 'promotional',
                'template' => [
                    'title' => 'Special Offer Just for You!',
                    'message' => 'Get {discount}% off your next order with code {promo_code}.',
                    'variables' => ['discount', 'promo_code', 'expires_at'],
                ],
            ],
        ];

        foreach ($types as $typeData) {
            NotificationType::updateOrCreate(
                ['name' => $typeData['name']],
                $typeData
            );
        }

        $this->command->info('Notification types seeded successfully.');
    }
}
