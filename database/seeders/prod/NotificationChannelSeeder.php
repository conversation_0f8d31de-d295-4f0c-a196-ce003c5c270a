<?php

declare(strict_types=1);

namespace Database\Seeders\Prod;

use App\Enums\System\NotificationChannel as NotificationChannelEnum;
use App\Models\User\NotificationChannel;
use Illuminate\Database\Seeder;

class NotificationChannelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding notification channels...');

        $channels = [
            [
                'name' => 'In-App Notifications',
                'description' => 'Notifications displayed within the application interface',
                'channel_type' => NotificationChannelEnum::IN_APP,
                'is_active' => true,
                'provider_settings' => [
                    'max_retention_days' => 30,
                    'auto_mark_read_after_days' => 7,
                    'supports_rich_content' => true,
                    'supports_actions' => true,
                ],
            ],
            [
                'name' => 'Push Notifications',
                'description' => 'Mobile and web push notifications via FCM',
                'channel_type' => NotificationChannelEnum::PUSH,
                'is_active' => true,
                'provider_settings' => [
                    'provider' => 'fcm',
                    'supports_rich_content' => true,
                    'supports_actions' => true,
                    'supports_images' => true,
                    'max_title_length' => 65,
                    'max_body_length' => 240,
                    'priority_levels' => ['normal', 'high'],
                ],
            ],
            [
                'name' => 'Email Notifications',
                'description' => 'Email notifications via ZeptoMail SMTP',
                'channel_type' => NotificationChannelEnum::EMAIL,
                'is_active' => true,
                'provider_settings' => [
                    'provider' => 'zeptomail',
                    'smtp_host' => 'smtp.zeptomail.com',
                    'smtp_port' => 587,
                    'supports_html' => true,
                    'supports_attachments' => true,
                    'supports_templates' => true,
                    'max_recipients' => 50,
                    'rate_limit_per_minute' => 100,
                ],
            ],
            [
                'name' => 'SMS Notifications',
                'description' => 'SMS notifications via Twilio',
                'channel_type' => NotificationChannelEnum::SMS,
                'is_active' => true,
                'provider_settings' => [
                    'provider' => 'twilio',
                    'supports_unicode' => true,
                    'max_message_length' => 160,
                    'max_unicode_length' => 70,
                    'supports_delivery_status' => true,
                    'rate_limit_per_minute' => 60,
                    'cost_per_message' => 0.0075, // USD
                ],
            ],
        ];

        foreach ($channels as $channelData) {
            NotificationChannel::updateOrCreate(
                [
                    'channel_type' => $channelData['channel_type'],
                ],
                $channelData
            );
        }

        $this->command->info('Notification channels seeded successfully.');
    }
}
