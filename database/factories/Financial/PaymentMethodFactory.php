<?php

declare(strict_types=1);

namespace Database\Factories\Financial;

use App\Enums\Financial\PaymentMethodProvider;
use App\Enums\Financial\PaymentMethodType;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Financial\PaymentMethod>
 */
class PaymentMethodFactory extends Factory
{
    protected $model = \App\Models\Financial\PaymentMethod::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'provider' => PaymentMethodProvider::PAYSTACK,
            'reference' => $this->faker->uuid(),
            'type' => $this->faker->randomElement(PaymentMethodType::cases()),
            'details' => $this->generatePaymentDetails(),
            'is_default' => false,
            'expires_at' => $this->faker->optional(0.7)->dateTimeBetween('now', '+5 years'),
        ];
    }

    /**
     * Generate payment details based on type.
     */
    private function generatePaymentDetails(): array
    {
        $type = $this->faker->randomElement(PaymentMethodType::cases());

        return match ($type) {
            PaymentMethodType::CARD => $this->generateCardDetails(),
            PaymentMethodType::BANK_ACCOUNT => $this->generateBankAccountDetails(),
            PaymentMethodType::WALLET => $this->generateWalletDetails(),
        };
    }

    /**
     * Generate card details.
     */
    private function generateCardDetails(): array
    {
        $cardTypes = ['visa', 'mastercard', 'verve'];
        $nigerianBanks = [
            'Access Bank', 'GTBank', 'First Bank', 'UBA', 'Zenith Bank',
            'Fidelity Bank', 'FCMB', 'Sterling Bank', 'Wema Bank', 'Union Bank',
        ];

        return [
            'last_four' => $this->faker->numerify('####'),
            'card_type' => $this->faker->randomElement($cardTypes),
            'bank' => $this->faker->randomElement($nigerianBanks),
            'bin' => $this->faker->numerify('######'),
            'exp_month' => $this->faker->numberBetween(1, 12),
            'exp_year' => $this->faker->numberBetween(2024, 2030),
            'country_code' => 'NG',
        ];
    }

    /**
     * Generate bank account details.
     */
    private function generateBankAccountDetails(): array
    {
        $nigerianBanks = [
            ['name' => 'Access Bank', 'code' => '044'],
            ['name' => 'GTBank', 'code' => '058'],
            ['name' => 'First Bank', 'code' => '011'],
            ['name' => 'UBA', 'code' => '033'],
            ['name' => 'Zenith Bank', 'code' => '057'],
            ['name' => 'Fidelity Bank', 'code' => '070'],
            ['name' => 'FCMB', 'code' => '214'],
            ['name' => 'Sterling Bank', 'code' => '232'],
            ['name' => 'Wema Bank', 'code' => '035'],
            ['name' => 'Union Bank', 'code' => '032'],
            ['name' => 'Stanbic IBTC', 'code' => '221'],
            ['name' => 'Ecobank', 'code' => '050'],
        ];

        $bank = $this->faker->randomElement($nigerianBanks);

        return [
            'account_number' => $this->faker->numerify('##########'),
            'account_name' => $this->faker->name(),
            'bank_name' => $bank['name'],
            'bank_code' => $bank['code'],
            'country_code' => 'NG',
        ];
    }

    /**
     * Generate wallet details.
     */
    private function generateWalletDetails(): array
    {
        $walletProviders = ['paystack', 'flutterwave', 'opay', 'palmpay', 'kuda'];

        return [
            'wallet_provider' => $this->faker->randomElement($walletProviders),
            'wallet_id' => $this->faker->uuid(),
            'phone_number' => $this->generateNigerianPhoneNumber(),
        ];
    }

    /**
     * Generate a Nigerian phone number.
     */
    private function generateNigerianPhoneNumber(): string
    {
        $prefixes = ['0701', '0702', '0703', '0704', '0705', '0706', '0707', '0708', '0709',
            '0802', '0803', '0804', '0805', '0806', '0807', '0808', '0809',
            '0810', '0811', '0812', '0813', '0814', '0815', '0816', '0817', '0818', '0819',
            '0901', '0902', '0903', '0904', '0905', '0906', '0907', '0908', '0909'];

        $prefix = $this->faker->randomElement($prefixes);
        $suffix = $this->faker->numerify('#######');

        return $prefix.$suffix;
    }

    /**
     * Create card payment method.
     */
    public function card(): static
    {
        return $this->state(fn () => [
            'type' => PaymentMethodType::CARD,
            'details' => $this->generateCardDetails(),
            'expires_at' => $this->faker->dateTimeBetween('now', '+5 years'),
        ]);
    }

    /**
     * Create bank account payment method.
     */
    public function bankAccount(): static
    {
        return $this->state(fn () => [
            'type' => PaymentMethodType::BANK_ACCOUNT,
            'details' => $this->generateBankAccountDetails(),
            'expires_at' => null,
        ]);
    }

    /**
     * Create wallet payment method.
     */
    public function wallet(): static
    {
        return $this->state(fn () => [
            'type' => PaymentMethodType::WALLET,
            'details' => $this->generateWalletDetails(),
            'expires_at' => null,
        ]);
    }

    /**
     * Create Visa card.
     */
    public function visa(): static
    {
        return $this->card()->state(fn () => [
            'details' => array_merge($this->generateCardDetails(), [
                'card_type' => 'visa',
                'bin' => '4'.$this->faker->numerify('#####'),
            ]),
        ]);
    }

    /**
     * Create Mastercard.
     */
    public function mastercard(): static
    {
        return $this->card()->state(fn () => [
            'details' => array_merge($this->generateCardDetails(), [
                'card_type' => 'mastercard',
                'bin' => '5'.$this->faker->numerify('#####'),
            ]),
        ]);
    }

    /**
     * Create Verve card (Nigerian local card).
     */
    public function verve(): static
    {
        return $this->card()->state(fn () => [
            'details' => array_merge($this->generateCardDetails(), [
                'card_type' => 'verve',
                'bin' => '506'.$this->faker->numerify('###'),
            ]),
        ]);
    }

    /**
     * Create GTBank account.
     */
    public function gtbank(): static
    {
        return $this->bankAccount()->state(fn () => [
            'details' => array_merge($this->generateBankAccountDetails(), [
                'bank_name' => 'GTBank',
                'bank_code' => '058',
            ]),
        ]);
    }

    /**
     * Create Access Bank account.
     */
    public function accessBank(): static
    {
        return $this->bankAccount()->state(fn () => [
            'details' => array_merge($this->generateBankAccountDetails(), [
                'bank_name' => 'Access Bank',
                'bank_code' => '044',
            ]),
        ]);
    }

    /**
     * Create First Bank account.
     */
    public function firstBank(): static
    {
        return $this->bankAccount()->state(fn () => [
            'details' => array_merge($this->generateBankAccountDetails(), [
                'bank_name' => 'First Bank',
                'bank_code' => '011',
            ]),
        ]);
    }

    /**
     * Create default payment method.
     */
    public function default(): static
    {
        return $this->state(fn () => [
            'is_default' => true,
        ]);
    }

    /**
     * Create expired payment method.
     */
    public function expired(): static
    {
        return $this->state(fn () => [
            'expires_at' => $this->faker->dateTimeBetween('-2 years', '-1 day'),
        ]);
    }

    /**
     * Create payment method for specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn () => [
            'user_id' => $user->id,
        ]);
    }
}
