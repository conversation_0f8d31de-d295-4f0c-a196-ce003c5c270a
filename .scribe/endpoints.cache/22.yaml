## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Customer Pickup'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/customer/pickup/available-slots
    metadata:
      groupName: 'Customer Pickup'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get available pickup slots for a business'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      business_id:
        name: business_id
        description: 'Business ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date:
        name: date
        description: 'Date in Y-m-d format.'
        required: true
        example: '2024-01-22'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      branch_id:
        name: branch_id
        description: 'Filter by business branch ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      business_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      date: '2024-01-22'
      branch_id: 019723aa-3202-70dd-a0c1-3565681dd87c
    bodyParameters:
      business_id:
        name: business_id
        description: 'The <code>id</code> of an existing record in the businesses table.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date:
        name: date
        description: 'Must be a valid date. Must be a date after or equal to <code>today</code>.'
        required: true
        example: '2051-06-30'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      branch_id:
        name: branch_id
        description: 'The <code>id</code> of an existing record in the business_branches table.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      business_id: architecto
      date: '2051-06-30'
      branch_id: architecto
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Available pickup slots retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "day_of_week": 1,
                "day_name": "Monday",
                "start_time": "09:00",
                "end_time": "12:00",
                "time_range": "09:00 - 12:00",
                "display_name": "Monday 09:00 - 12:00",
                "max_orders": 20,
                "has_limit": true,
                "is_active": true,
                "current_orders_count": 5,
                "remaining_capacity": 15,
                "is_currently_open": false,
                "next_occurrence": "2024-01-22T09:00:00Z",
                "business_branch": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "name": "Main Branch"
                }
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/customer/pickup/book-slot
    metadata:
      groupName: 'Customer Pickup'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Book a pickup slot for an order'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      order_id:
        name: order_id
        description: 'Order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87d
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      pickup_slot_id:
        name: pickup_slot_id
        description: 'Pickup slot ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      pickup_date:
        name: pickup_date
        description: 'Pickup date in Y-m-d format.'
        required: true
        example: '2024-01-22'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      order_id: 019723aa-3202-70dd-a0c1-3565681dd87d
      pickup_slot_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      pickup_date: '2024-01-22'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup slot booked successfully",
            "data": {
              "order_id": "019723aa-3202-70dd-a0c1-3565681dd87d",
              "pickup_slot_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "scheduled_pickup_time": "2024-01-22T09:00:00Z",
              "pickup_slot": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "day_name": "Monday",
                "time_range": "09:00 - 12:00",
                "display_name": "Monday 09:00 - 12:00"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/customer/pickup/orders/{order_id}/cancel-slot'
    metadata:
      groupName: 'Customer Pickup'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel a pickup slot booking'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order_id:
        name: order_id
        description: 'The ID of the order.'
        required: true
        example: 019745aa-69fe-7301-9a54-ac8c1cc78706
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87d
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order_id: 019745aa-69fe-7301-9a54-ac8c1cc78706
      order: 019723aa-3202-70dd-a0c1-3565681dd87d
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup slot cancelled successfully",
            "data": {
              "order_id": "019723aa-3202-70dd-a0c1-3565681dd87d",
              "pickup_slot_id": null,
              "scheduled_pickup_time": null
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/pickup/orders
    metadata:
      groupName: 'Customer Pickup'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get customer's pickup orders"
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      status:
        name: status
        description: 'Filter by order status.'
        required: false
        example: ready_for_pickup
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Filter by business ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      status: ready_for_pickup
      business_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      page: 1
      per_page: 15
    bodyParameters:
      status:
        name: status
        description: ''
        required: false
        example: completed
        type: string
        enumValues:
          - pending
          - accepted
          - preparing
          - ready_for_pickup
          - completed
          - cancelled
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'The <code>id</code> of an existing record in the businesses table.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 7
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      status: completed
      business_id: architecto
      page: 22
      per_page: 7
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup orders retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
                "order_reference": "ORD-2024-001",
                "status": "ready_for_pickup",
                "total_amount": 2500,
                "scheduled_pickup_time": "2024-01-22T09:00:00Z",
                "business": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "Test Restaurant"
                },
                "pickup_slot": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "day_name": "Monday",
                  "time_range": "09:00 - 12:00",
                  "display_name": "Monday 09:00 - 12:00"
                }
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 15,
              "total": 5,
              "last_page": 1
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
