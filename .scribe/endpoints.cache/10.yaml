## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Admin Staff Activities'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/activities
    metadata:
      groupName: 'Admin Staff Activities'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get staff activities with filtering and pagination.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      activity_type:
        name: activity_type
        description: 'Filter by activity type.'
        required: false
        example: order_update
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      activity_category:
        name: activity_category
        description: 'Filter by category (authentication, order_management, inventory, etc.).'
        required: false
        example: order_management
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      severity:
        name: severity
        description: 'Filter by severity (low, medium, high, critical).'
        required: false
        example: high
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sensitive_only:
        name: sensitive_only
        description: 'Show only sensitive activities.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_id:
        name: user_id
        description: 'Filter by specific user ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Filter by specific business ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_date:
        name: start_date
        description: 'Start date for filtering (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'End date for filtering (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (1-100).'
        required: false
        example: 25
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      activity_type: order_update
      activity_category: order_management
      severity: high
      sensitive_only: true
      user_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      business_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      start_date: '2024-01-01'
      end_date: '2024-01-31'
      per_page: 25
    bodyParameters:
      activity_type:
        name: activity_type
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      activity_category:
        name: activity_category
        description: ''
        required: false
        example: inventory
        type: string
        enumValues:
          - authentication
          - order_management
          - inventory
          - user_management
          - payment
          - delivery
          - settings
          - security
        exampleWasSpecified: false
        nullable: false
        custom: []
      severity:
        name: severity
        description: ''
        required: false
        example: critical
        type: string
        enumValues:
          - low
          - medium
          - high
          - critical
        exampleWasSpecified: false
        nullable: false
        custom: []
      sensitive_only:
        name: sensitive_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user_id:
        name: user_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the businesses table.'
        required: false
        example: c90237e9-ced5-3af6-88ea-84aeaa148878
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      start_date:
        name: start_date
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-06T17:54:14'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'Must be a valid date. Must be a date after or equal to <code>start_date</code>.'
        required: false
        example: '2051-06-30'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      activity_type: architecto
      activity_category: inventory
      severity: critical
      sensitive_only: false
      user_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
      business_id: c90237e9-ced5-3af6-88ea-84aeaa148878
      start_date: '2025-06-06T17:54:14'
      end_date: '2051-06-30'
      per_page: 22
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Staff activities retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "activity_type": "order_update",
                "activity_category": "order_management",
                "description": "Order updated: ORD-2024-ABC123",
                "severity": "low",
                "is_sensitive": false,
                "user": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "John Doe",
                  "email": "<EMAIL>"
                },
                "business": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "name": "Coffee Shop"
                },
                "subject_type": "App\\Models\\Order",
                "subject_id": "019723aa-3202-70dd-a0c1-3565681dd87d",
                "changes": {
                  "status": {
                    "old": "pending",
                    "new": "confirmed"
                  }
                },
                "ip_address": "***********",
                "created_at": "2024-01-22T15:30:00Z"
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 25,
              "total": 150,
              "last_page": 6
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/activities/statistics
    metadata:
      groupName: 'Admin Staff Activities'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get activity statistics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      start_date:
        name: start_date
        description: 'Start date for statistics (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'End date for statistics (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Filter by specific business ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      start_date: '2024-01-01'
      end_date: '2024-01-31'
      business_id: 019723aa-3202-70dd-a0c1-3565681dd87a
    bodyParameters:
      start_date:
        name: start_date
        description: 'Must be a valid date.'
        required: false
        example: '2025-06-06T17:54:14'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      end_date:
        name: end_date
        description: 'Must be a valid date. Must be a date after or equal to <code>start_date</code>.'
        required: false
        example: '2051-06-30'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the businesses table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      start_date: '2025-06-06T17:54:14'
      end_date: '2051-06-30'
      business_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Activity statistics retrieved successfully",
            "data": {
              "total_activities": 1250,
              "sensitive_activities": 45,
              "activities_by_category": {
                "order_management": 650,
                "inventory": 300,
                "authentication": 200,
                "user_management": 50,
                "payment": 30,
                "settings": 20
              },
              "activities_by_severity": {
                "low": 1000,
                "medium": 200,
                "high": 40,
                "critical": 10
              },
              "top_users": [
                {
                  "user_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "user_name": "John Doe",
                  "activity_count": 150
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/activities/critical
    metadata:
      groupName: 'Admin Staff Activities'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get recent critical activities.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'Number of activities to retrieve (1-100).'
        required: false
        example: 50
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 50
    bodyParameters:
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      limit: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Critical activities retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "activity_type": "user_suspended",
                "description": "User suspended: John Doe (<EMAIL>)",
                "severity": "critical",
                "user": {
                  "name": "Admin User",
                  "email": "<EMAIL>"
                },
                "created_at": "2024-01-22T15:30:00Z"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/activities/{activity}'
    metadata:
      groupName: 'Admin Staff Activities'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get activity details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      activity:
        name: activity
        description: 'Activity ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      activity: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Activity details retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "activity_type": "order_update",
              "activity_category": "order_management",
              "description": "Order updated: ORD-2024-ABC123",
              "severity": "low",
              "is_sensitive": false,
              "user": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "name": "John Doe",
                "email": "<EMAIL>"
              },
              "old_values": {
                "status": "pending",
                "updated_at": "2024-01-22T15:25:00Z"
              },
              "new_values": {
                "status": "confirmed",
                "updated_at": "2024-01-22T15:30:00Z"
              },
              "metadata": {
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0...",
                "session_id": "abc123def456"
              },
              "created_at": "2024-01-22T15:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
