## Autogenerated by <PERSON>ri<PERSON>. DO NOT MODIFY.

name: 'Google Maps'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/maps/geocode
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Geocode an address to get coordinates.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      address:
        name: address
        description: 'Must not be greater than 500 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      country:
        name: country
        description: 'Must be 2 characters.'
        required: false
        example: ng
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      address: b
      country: ng
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/maps/reverse-geocode
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reverse geocode coordinates to get address.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      latitude:
        name: latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      latitude: -89
      longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/maps/distance
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Calculate distance and duration between two points.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      origin_latitude:
        name: origin_latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      origin_longitude:
        name: origin_longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination_latitude:
        name: destination_latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -90
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination_longitude:
        name: destination_longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      mode:
        name: mode
        description: ''
        required: false
        example: transit
        type: string
        enumValues:
          - driving
          - walking
          - bicycling
          - transit
        exampleWasSpecified: false
        nullable: true
        custom: []
      avoid_tolls:
        name: avoid_tolls
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      avoid_highways:
        name: avoid_highways
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      origin_latitude: -89
      origin_longitude: -179
      destination_latitude: -90
      destination_longitude: -179
      mode: transit
      avoid_tolls: true
      avoid_highways: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/maps/route
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get optimized route with waypoints.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      origin:
        name: origin
        description: ''
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      origin.lat:
        name: origin.lat
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      origin.lng:
        name: origin.lng
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination:
        name: destination
        description: ''
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination.lat:
        name: destination.lat
        description: 'Must be between -90 and 90.'
        required: true
        example: -90
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination.lng:
        name: destination.lng
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      waypoints:
        name: waypoints
        description: ''
        required: false
        example: null
        type: 'object[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      optimize_waypoints:
        name: optimize_waypoints
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'waypoints[].lat':
        name: 'waypoints[].lat'
        description: 'This field is required when <code>waypoints</code> is present. Must be between -90 and 90.'
        required: false
        example: -90
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'waypoints[].lng':
        name: 'waypoints[].lng'
        description: 'This field is required when <code>waypoints</code> is present. Must be between -180 and 180.'
        required: false
        example: -180
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      origin:
        lat: -89
        lng: -179
      destination:
        lat: -90
        lng: -179
      optimize_waypoints: false
      waypoints:
        -
          lat: -90
          lng: -180
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/maps/validate-address
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Validate an address.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      address:
        name: address
        description: 'Must not be greater than 500 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      address: b
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/maps/delivery-eta
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Calculate delivery ETA.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      origin_latitude:
        name: origin_latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      origin_longitude:
        name: origin_longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination_latitude:
        name: destination_latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -90
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      destination_longitude:
        name: destination_longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      pickup_time_minutes:
        name: pickup_time_minutes
        description: 'Must be at least 0. Must not be greater than 120.'
        required: false
        example: 17
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      origin_latitude: -89
      origin_longitude: -179
      destination_latitude: -90
      destination_longitude: -179
      pickup_time_minutes: 17
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/maps/test-connection
    metadata:
      groupName: 'Google Maps'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Test Google Maps API connection.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.586047Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 350e8044-0cbe-43cb-ba5b-d88c8d1e2c86
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-uWuMMxGD98ZMslq2UviMgg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
