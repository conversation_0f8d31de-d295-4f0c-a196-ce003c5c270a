## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Central Customer - Business Search'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/customer/search/businesses
    metadata:
      groupName: 'Central Customer - Business Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Search businesses.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query for business name, description, etc.'
        required: false
        example: restaurant
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Filter by business type.'
        required: false
        example: restaurant
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by business status.'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Filter by country.'
        required: false
        example: NG
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'Filter by state.'
        required: false
        example: LA
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Filter businesses that allow pickup.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Filter businesses that accept COD.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'Sort results (relevance, name_asc, name_desc, newest, oldest, rating).'
        required: false
        example: relevance
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: restaurant
      business_type: restaurant
      status: active
      country_id: NG
      state_id: LA
      allows_pickup: true
      accepts_cash_on_delivery: true
      sort: relevance
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Businesses found successfully",
            "data": {
              "data": [
                {
                  "id": "uuid",
                  "business_name": "Sample Restaurant",
                  "business_type": "restaurant",
                  "description": "Great food and service",
                  "status": "active",
                  "allows_pickup": true,
                  "accepts_cash_on_delivery": true
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/businesses/suggestions
    metadata:
      groupName: 'Central Customer - Business Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business search suggestions.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query.'
        required: true
        example: rest
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Number of suggestions (max 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: rest
      limit: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search suggestions retrieved successfully",
            "data": [
              "Restaurant ABC",
              "Restaurant XYZ",
              "Rest Stop Cafe"
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/businesses/trending
    metadata:
      groupName: 'Central Customer - Business Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get trending businesses.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'Number of trending businesses (max 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Trending businesses retrieved successfully",
            "data": [
              {
                "id": "uuid",
                "business_name": "Trending Restaurant",
                "business_type": "restaurant",
                "status": "active"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
