## Autogenerated by Scribe. DO NOT MODIFY.

name: Public
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/pricing
    metadata:
      groupName: Public
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get subscription pricing for landing page.'
      description: 'Public endpoint - no authentication required.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "business_plans": [
                {
                  "id": "plan-uuid",
                  "name": "Business Starter",
                  "slug": "business_starter",
                  "description": "Enhanced plan for growing businesses",
                  "target_type": "business",
                  "plan_prices": [
                    {
                      "billing_interval": "monthly",
                      "price": "10000.00",
                      "currency": "NGN",
                      "formatted_price": "NGN 10,000.00"
                    }
                  ],
                  "features": [
                    {
                      "name": "Basic Order Management",
                      "slug": "basic_order_management",
                      "limit": null
                    }
                  ]
                }
              ],
              "provider_plans": [
                {
                  "id": "plan-uuid",
                  "name": "Provider Starter",
                  "slug": "provider_starter",
                  "description": "Enhanced plan for small fleet providers",
                  "target_type": "provider",
                  "plan_prices": [
                    {
                      "billing_interval": "monthly",
                      "price": "10000.00",
                      "currency": "NGN",
                      "formatted_price": "NGN 10,000.00"
                    }
                  ],
                  "features": [
                    {
                      "name": "Basic Fleet Management",
                      "slug": "basic_fleet_management",
                      "limit": null
                    }
                  ]
                }
              ]
            },
            "message": "Subscription pricing retrieved successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
