## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Chat System'
description: |-

  APIs for real-time chat communication between customers, businesses, and drivers
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/chat/messages
    metadata:
      groupName: 'Chat System'
      groupDescription: |-

        APIs for real-time chat communication between customers, businesses, and drivers
      subgroup: ''
      subgroupDescription: ''
      title: 'Send Chat Message'
      description: 'Send a message in a chat conversation.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      conversation_id:
        name: conversation_id
        description: 'The conversation ID.'
        required: true
        example: customer-business-uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      message:
        name: message
        description: 'The message content.'
        required: true
        example: '"Hello, when will my order be ready?"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      message_type:
        name: message_type
        description: 'optional The message type.'
        required: false
        example: text
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      metadata:
        name: metadata
        description: 'optional Additional message metadata.'
        required: false
        example:
          order_id: uuid
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      conversation_id: customer-business-uuid
      message: '"Hello, when will my order be ready?"'
      message_type: text
      metadata:
        order_id: uuid
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Message sent successfully",
            "data": {
              "message_id": "uuid",
              "conversation_id": "customer-business-uuid",
              "content": "Hello, when will my order be ready?",
              "type": "text",
              "sent_at": "2024-01-15T10:30:00Z",
              "sender": {
                "id": "uuid",
                "name": "John Customer",
                "type": "customer"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/chat/conversations/{conversation_id}/messages'
    metadata:
      groupName: 'Chat System'
      groupDescription: |-

        APIs for real-time chat communication between customers, businesses, and drivers
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Conversation Messages'
      description: 'Get messages from a conversation.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      conversation_id:
        name: conversation_id
        description: 'The conversation ID.'
        required: true
        example: customer-business-uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      conversation_id: customer-business-uuid
    queryParameters:
      limit:
        name: limit
        description: 'optional Number of messages to retrieve.'
        required: false
        example: 50
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      before:
        name: before
        description: 'optional Get messages before this message ID.'
        required: false
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 50
      before: uuid
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Messages retrieved successfully",
            "data": {
              "conversation_id": "customer-business-uuid",
              "messages": [
                {
                  "id": "uuid",
                  "content": "Hello, when will my order be ready?",
                  "type": "text",
                  "sent_at": "2024-01-15T10:30:00Z",
                  "sender": {
                    "id": "uuid",
                    "name": "John Customer",
                    "type": "customer"
                  }
                }
              ],
              "has_more": false
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/chat/conversations
    metadata:
      groupName: 'Chat System'
      groupDescription: |-

        APIs for real-time chat communication between customers, businesses, and drivers
      subgroup: ''
      subgroupDescription: ''
      title: 'Get User Conversations'
      description: 'Get all conversations for the current user.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Conversations retrieved successfully",
            "data": [
              {
                "conversation_id": "customer-business-uuid",
                "type": "customer-business",
                "participants": [
                  {
                    "id": "uuid",
                    "name": "John Customer",
                    "type": "customer"
                  },
                  {
                    "id": "uuid",
                    "name": "Mario's Pizza",
                    "type": "business"
                  }
                ],
                "last_message": {
                  "content": "Your order is ready for pickup!",
                  "sent_at": "2024-01-15T11:00:00Z",
                  "sender_name": "Mario's Pizza"
                },
                "unread_count": 2
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/chat/conversations
    metadata:
      groupName: 'Chat System'
      groupDescription: |-

        APIs for real-time chat communication between customers, businesses, and drivers
      subgroup: ''
      subgroupDescription: ''
      title: 'Create or Get Conversation'
      description: 'Create a new conversation or get existing one between participants.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      type:
        name: type
        description: 'Conversation type.'
        required: true
        example: customer-business
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      participant_id:
        name: participant_id
        description: "The other participant's ID."
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      context:
        name: context
        description: 'optional Conversation context (e.g., order_id).'
        required: false
        example:
          order_id: uuid
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      type: customer-business
      participant_id: uuid
      context:
        order_id: uuid
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Conversation created successfully",
            "data": {
              "conversation_id": "customer-business-uuid",
              "type": "customer-business",
              "created": true,
              "participants": [
                {
                  "id": "uuid",
                  "name": "John Customer",
                  "type": "customer"
                },
                {
                  "id": "uuid",
                  "name": "Mario's Pizza",
                  "type": "business"
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
