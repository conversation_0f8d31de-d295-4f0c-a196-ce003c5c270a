## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'WhatsApp Webhooks'
description: |-

  Handle WhatsApp Business API webhooks for message delivery and status updates
endpoints:
  -
    httpMethods:
      - GET
    uri: api/webhooks/whatsapp
    metadata:
      groupName: 'WhatsApp Webhooks'
      groupDescription: |-

        Handle WhatsApp Business API webhooks for message delivery and status updates
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify WhatsApp Webhook'
      description: |-
        Verify webhook subscription with WhatsApp Business API.
        This endpoint is called by <PERSON>s<PERSON>pp to verify the webhook URL.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      hub.mode:
        name: hub.mode
        description: 'The mode parameter from WhatsApp.'
        required: true
        example: subscribe
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      hub.challenge:
        name: hub.challenge
        description: 'The challenge parameter from WhatsApp.'
        required: true
        example: '1234567890'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      hub.verify_token:
        name: hub.verify_token
        description: 'The verify token configured in WhatsApp.'
        required: true
        example: your_verify_token
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '1234567890'
        headers: []
        description: ''
        custom: []
      -
        status: 403
        content: |-
          {
            "error": "Forbidden"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/webhooks/whatsapp
    metadata:
      groupName: 'WhatsApp Webhooks'
      groupDescription: |-

        Handle WhatsApp Business API webhooks for message delivery and status updates
      subgroup: ''
      subgroupDescription: ''
      title: 'Handle WhatsApp Webhook'
      description: |-
        Process incoming WhatsApp messages and status updates.
        This endpoint receives webhooks from WhatsApp Business API.
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      object:
        name: object
        description: 'The webhook payload from WhatsApp Business API.'
        required: true
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      object.entry:
        name: object.entry
        description: 'Array of webhook entries.'
        required: true
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'object.entry.*.id':
        name: 'object.entry.*.id'
        description: 'Business account ID.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'object.entry.*.changes':
        name: 'object.entry.*.changes'
        description: 'Array of changes/events.'
        required: true
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'object.entry.*.changes.*.value':
        name: 'object.entry.*.changes.*.value'
        description: 'The event data.'
        required: true
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'object.entry.*.changes.*.value.messages':
        name: 'object.entry.*.changes.*.value.messages'
        description: 'optional Array of incoming messages.'
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'object.entry.*.changes.*.value.statuses':
        name: 'object.entry.*.changes.*.value.statuses'
        description: 'optional Array of message status updates.'
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      object:
        entry:
          0: architecto
          '*':
            changes:
              '*':
                value:
                  messages:
                    - architecto
                  statuses:
                    - architecto
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "status": "success"
          }
        headers: []
        description: ''
        custom: []
      -
        status: 400
        content: |-
          {
            "status": "error",
            "message": "Invalid webhook payload"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
