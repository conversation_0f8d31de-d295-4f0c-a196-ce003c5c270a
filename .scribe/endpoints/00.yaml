name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/health
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Basic health check endpoint.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"System is healthy","timestamp":"2025-06-06T17:54:14.332418Z","data":{"status":"ok","service":"DeliveryNexus API","version":"1.0.0"},"request_id":"b4c231ea-8098-4f93-98b3-935ec7c3bddc"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b4c231ea-8098-4f93-98b3-935ec7c3bddc
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-w1Nz+sDypUHDgjqfF5qfcg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/health/detailed
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Detailed health check with dependencies.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"All systems operational","timestamp":"2025-06-06T17:54:14.363976Z","data":{"status":"ok","service":"DeliveryNexus API","version":"1.0.0","checks":{"database":{"status":"ok","message":"Database connection successful","connection":"pgsql","response_time":0.31},"cache":{"status":"ok","message":"Cache working properly","response_time":0.18},"redis":{"status":"ok","message":"Redis working properly","response_time":0.38},"queue":{"status":"ok","message":"Queue connection successful","connection":"redis"}},"system":{"php_version":"8.4.7","laravel_version":"12.16.0","memory_usage":"52.5 MB","disk_usage":"352.1 GB (38.01%)"}},"request_id":"951489df-1616-4e05-85b4-fbe8e29bcd26"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 951489df-1616-4e05-85b4-fbe8e29bcd26
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-tXcb4+TeuL/wwFNXJnzuvA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Central domain login - Only allows users with no tenant_id.'
      description: 'Used by customers and platform admins on api.deliverynexus.com'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: "User's email address or phone number."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: "User's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      remember_me:
        name: remember_me
        description: 'Whether to remember the user for extended login.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      password: password123
      remember_me: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/password/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send password reset code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: 'Email address or phone number to send password reset instructions.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/password/confirm
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset password with token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: 'Email address or phone number for password reset.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      token:
        name: token
        description: 'Password reset token received via email/SMS.'
        required: true
        example: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'New password for the account.'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
      password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/user-context
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user context for mobile app login routing.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 1: Register a new user (simple email + password only).'
      description: 'Registration only happens on central domain.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Full name of the user. Must not be greater than 255 characters.'
        required: true
        example: 'John Doe'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the account. Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Nigerian phone number (optional, format: +234XXXXXXXXXX). Must match the regex /^\+234[0-9]{10}$/.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      timezone:
        name: timezone
        description: 'User timezone (optional, defaults to Africa/Lagos). Must be a valid time zone, such as <code>Africa/Accra</code>.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      first_name:
        name: first_name
        description: 'First name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'John Doe'
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      timezone: Africa/Lagos
      first_name: John
      last_name: Doe
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/select-account-type
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 2: Select account type (customer, business, or delivery provider).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      account_type:
        name: account_type
        description: 'Type of account to create - "customer", "business", or "delivery_provider".'
        required: true
        example: customer
        type: string
        enumValues:
          - customer
          - business
          - delivery_provider
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      account_type: customer
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/onboard/business
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 3: Business onboarding (creates tenant + business with minimal data).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business - "food", "retail", "service", "logistics".'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the business (optional). Must not be greater than 500 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the business (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the business (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/onboard/provider
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 3: Provider onboarding (creates tenant + provider with minimal data).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      provider_name:
        name: provider_name
        description: 'Name of the delivery provider. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Swift Delivery Services'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_type:
        name: provider_type
        description: 'Type of delivery provider - "individual", "company", "fleet".'
        required: true
        example: company
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the provider operates. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the delivery provider (optional). Must not be greater than 500 characters.'
        required: false
        example: 'Professional delivery services covering Lagos and surrounding areas'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the provider (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the provider (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      provider_name: 'Swift Delivery Services'
      provider_type: company
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'Professional delivery services covering Lagos and surrounding areas'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register/invitation
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register from staff invitation.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      invitation_token:
        name: invitation_token
        description: 'Invitation token received via email. The <code>token</code> of an existing record in the team_invitations table.'
        required: true
        example: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      first_name:
        name: first_name
        description: 'First name of the staff member. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Jane
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name of the staff member. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Smith
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account (minimum 8 characters). Must be at least 8 characters.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Phone number (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      invitation_token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
      first_name: Jane
      last_name: Smith
      password: SecurePassword123!
      phone_number: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/featured-businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get random businesses for homepage showcase, preferably around user location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Businesses retrieved successfully","timestamp":"2025-06-06T17:54:14.452623Z","data":[{"id":"019745aa-299d-7379-850a-0bf26ce8f712","tenant_id":"019745aa-299c-7130-a208-b6c32f402ac6","business_name":"Lagos Grill","business_type":"food","subdomain":"international-store","slug":"international-store","description":"Modern Nigerian cuisine with a contemporary twist. Grilled specialties and local favorites.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC674730","tax_identification_number":"85663508-4054","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1070,"max_order_value":33830,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":null,"tax_clearance":"http:\/\/www.roob.com\/quia-consequatur-debitis-sunt-sint-laborum-quis","business_permit":"http:\/\/www.cummings.com\/perspiciatis-dolorum-minima-non-veniam-et-sint-aut","owner_id":null,"bank_statement":"http:\/\/keeling.info\/nemo-nostrum-optio-non-rerum-sint.html"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-299f-7195-a925-88651c01435b","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-299f-7195-a925-88651c01435b","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"019745aa-29be-71ea-a143-82f3c782211d","tenant_id":"019745aa-29bc-73c1-9d02-471d5a0fb11e","business_name":"Express Supermarket","business_type":"retail","subdomain":"royal-kitchen","slug":"royal-kitchen","description":"Modern supermarket with a wide range of local and international products.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0011cc?text=business+non","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN604208","tax_identification_number":"58253136-6096","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1054,"max_order_value":24920,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":false,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/treutel.com\/quia-dolores-ab-qui-aut-sit-quo.html","tax_clearance":"http:\/\/nienow.com\/doloremque-sed-et-sit-omnis-eveniet","business_permit":"http:\/\/www.prohaska.org\/ex-nesciunt-quidem-fuga-quia-quasi-omnis","owner_id":null,"bank_statement":"http:\/\/www.pagac.com\/velit-et-illum-sunt-aut-earum-non-ut.html"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29bf-73cc-9e3f-f1d0d3bbc7c7","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29bf-73cc-9e3f-f1d0d3bbc7c7","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"019745aa-2995-7138-b981-554b96eff25d","tenant_id":"019745aa-2994-71f4-b550-642f64eb5c4a","business_name":"Suya King","business_type":"food","subdomain":"modern-phones","slug":"modern-phones","description":"Premium suya and grilled meat specialist. Fresh, spicy, and delicious.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/00cc77?text=business+maiores","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT784350","tax_identification_number":"88149329-7201","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1226,"max_order_value":24270,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":null,"tax_clearance":"http:\/\/www.leffler.info\/et-quia-autem-officia-dolorem-tempore-molestiae-et.html","business_permit":"http:\/\/www.gaylord.org\/","owner_id":"http:\/\/armstrong.org\/","bank_statement":"http:\/\/sporer.com\/eaque-tempore-ut-magni-error-voluptas-saepe-sed"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-2997-7048-820f-55d279967151","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-2997-7048-820f-55d279967151","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"019745aa-29f2-70b1-9cf7-8cbd8bdeea9a","tenant_id":"019745aa-29f1-723c-9797-a9f965f60d87","business_name":"Tech Support Hub","business_type":"service","subdomain":"royal-fashion-house","slug":"royal-fashion-house","description":"Expert technology support and IT services for businesses and individuals.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0077bb?text=business+illo","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","cac_registration_number":"BN461780","tax_identification_number":"41479275-2572","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1736,"max_order_value":11987,"verified_customers_only":true,"business_hours_only":true},"accepts_cash_on_delivery":false,"allows_pickup":false,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":null,"tax_clearance":"http:\/\/heathcote.com\/eos-facere-in-omnis-consequatur-at","business_permit":"https:\/\/www.gerlach.biz\/iure-reprehenderit-et-facilis","owner_id":"http:\/\/www.walsh.com\/veritatis-libero-consequatur-id-sapiente","bank_statement":null},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29f4-7023-8ba5-b9a213dc95ab","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29f4-7023-8ba5-b9a213dc95ab","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"019745aa-29b4-7136-835d-91c5076dee95","tenant_id":"019745aa-29b3-71e4-a472-b98153202a7d","business_name":"Quick Mart","business_type":"retail","subdomain":"modern-cafe","slug":"modern-cafe","description":"Your neighborhood convenience store for daily essentials and groceries.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC888592","tax_identification_number":"40754162-0707","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":525,"max_order_value":40472,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"https:\/\/pfeffer.com\/suscipit-accusantium-quam-blanditiis-ut-est.html","tax_clearance":null,"business_permit":"http:\/\/www.jaskolski.com\/itaque-aspernatur-adipisci-aut-velit-quam-commodi-qui.html","owner_id":"http:\/\/www.tremblay.com\/","bank_statement":null},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29b7-70c1-b6a6-22432e90f672","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29b7-70c1-b6a6-22432e90f672","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"019745aa-29c9-7062-9a0b-9f7fb7b32b8a","tenant_id":"019745aa-29c8-7225-b40f-df3cc18cab33","business_name":"Smart Store","business_type":"retail","subdomain":"lagos-bistro","slug":"lagos-bistro","description":"Technology-enabled retail store with smart shopping solutions.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/007722?text=business+doloribus","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC163018","tax_identification_number":"01099721-1074","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1114,"max_order_value":36945,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.wilderman.net\/voluptas-facere-quo-non-iusto-inventore","tax_clearance":"http:\/\/weimann.com\/iusto-voluptates-voluptatem-ut-cupiditate-a-ut","business_permit":"http:\/\/lesch.com\/perferendis-molestias-consectetur-sequi-laboriosam-quasi-sint.html","owner_id":"https:\/\/www.smith.org\/cupiditate-est-eos-cupiditate-dignissimos-facere-velit","bank_statement":null},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29cb-7328-9081-8aaf01b324a1","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29cb-7328-9081-8aaf01b324a1","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false}],"request_id":"143b92d2-c83a-4724-a3b6-90d36c8a32b2"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 143b92d2-c83a-4724-a3b6-90d36c8a32b2
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-VF1n6FZMCcJEx4pfuTTSJQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/featured-providers
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get random delivery providers for homepage showcase, preferably around user location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Delivery providers retrieved successfully","timestamp":"2025-06-06T17:54:14.470004Z","data":[{"id":"019745aa-4bbb-73e3-b681-f660641be00e","tenant_id":"019745aa-4bb6-7339-ab0f-289477d18314","company_name":"Kano Fast Courier","description":"Northern Nigeria delivery specialist","contact_email":"<EMAIL>","contact_phone":"08012345704","status":"verified","is_internal_provider":false,"performance_rating_avg":4.54,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:37.000000Z","updated_at":"2025-06-06T14:34:37.000000Z"},{"id":"019745aa-57ba-7121-94bc-958718aa9503","tenant_id":"019745aa-2990-7249-a8c0-dad8386a4059","company_name":"Jollof Palace Delivery","description":"Internal delivery service for Jollof Palace","contact_email":"<EMAIL>","contact_phone":"07063868544","status":"active","is_internal_provider":true,"performance_rating_avg":4.54,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:40.000000Z","updated_at":"2025-06-06T14:34:40.000000Z"},{"id":"019745aa-4089-72df-ad3c-3334bece3110","tenant_id":"019745aa-4084-73ab-a15c-20267087b396","company_name":"Port Harcourt Delivery Hub","description":"Reliable delivery solutions for Rivers State","contact_email":"<EMAIL>","contact_phone":"08012345703","status":"verified","is_internal_provider":false,"performance_rating_avg":3.7,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:35.000000Z","updated_at":"2025-06-06T14:34:35.000000Z"},{"id":"019745aa-564e-7053-b70b-344d6012059a","tenant_id":"019745aa-2981-73b1-8eec-3d9b73e15998","company_name":"Mama Cass Kitchen Delivery","description":"Internal delivery service for Mama Cass Kitchen","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","is_internal_provider":true,"performance_rating_avg":4.22,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:40.000000Z","updated_at":"2025-06-06T14:34:40.000000Z"},{"id":"019745aa-327a-70f3-a105-7645d1f80f3d","tenant_id":"019745aa-3275-709c-a13e-bb5c697071ac","company_name":"Lagos Express Logistics","description":"Premium delivery service for Lagos and surrounding areas","contact_email":"<EMAIL>","contact_phone":"08012345701","status":"verified","is_internal_provider":false,"performance_rating_avg":3.57,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:31.000000Z","updated_at":"2025-06-06T14:34:31.000000Z"},{"id":"019745aa-2aaa-7209-8a01-6d0838e2ed70","tenant_id":"019745aa-2aa6-7300-b5b7-b6c1f7eaed76","company_name":"Swift Delivery Nigeria","description":"Fast and reliable delivery service across Nigeria","contact_email":"<EMAIL>","contact_phone":"08012345700","status":"verified","is_internal_provider":false,"performance_rating_avg":4.06,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z"}],"request_id":"a01a46b5-eda4-4d17-a1a5-51405f944e09"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a01a46b5-eda4-4d17-a1a5-51405f944e09
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-xuUDrRSi0Vz439edZ95Fcw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get homepage statistics for showcase.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Homepage statistics retrieved successfully","timestamp":"2025-06-06T17:54:14.478331Z","data":{"total_businesses":22,"total_providers":8,"total_orders_completed":0,"cities_served":1},"request_id":"bba1b77b-6b26-4589-8f92-a522f4a1f105"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: bba1b77b-6b26-4589-8f92-a522f4a1f105
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-guW56QoizKKfqSnkgKn4Mw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/testimonials
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get recent success stories/testimonials for homepage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Testimonials retrieved successfully","timestamp":"2025-06-06T17:54:14.482548Z","data":[{"id":1,"customer_name":"John Doe","business_name":"Pizza Palace","rating":5,"comment":"Amazing service and fast delivery!","created_at":"2025-06-04T17:54:14.482483Z"},{"id":2,"customer_name":"Jane Smith","business_name":"Quick Logistics","rating":5,"comment":"Professional delivery service, highly recommended!","created_at":"2025-06-01T17:54:14.482525Z"}],"request_id":"76015e7d-c9e6-49fb-b412-490e2c2c0ae3"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 76015e7d-c9e6-49fb-b412-490e2c2c0ae3
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-oZ/yzM6AS15sJSqf8pHPNA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all homepage showcase data in one request.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Homepage showcase data retrieved successfully","timestamp":"2025-06-06T17:54:14.496315Z","data":{"featured_businesses":[{"id":"019745aa-2995-7138-b981-554b96eff25d","tenant_id":"019745aa-2994-71f4-b550-642f64eb5c4a","business_name":"Suya King","business_type":"food","subdomain":"modern-phones","slug":"modern-phones","description":"Premium suya and grilled meat specialist. Fresh, spicy, and delicious.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/00cc77?text=business+maiores","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT784350","tax_identification_number":"88149329-7201","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1226,"max_order_value":24270,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":null,"tax_clearance":"http:\/\/www.leffler.info\/et-quia-autem-officia-dolorem-tempore-molestiae-et.html","business_permit":"http:\/\/www.gaylord.org\/","owner_id":"http:\/\/armstrong.org\/","bank_statement":"http:\/\/sporer.com\/eaque-tempore-ut-magni-error-voluptas-saepe-sed"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-2997-7048-820f-55d279967151","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-2997-7048-820f-55d279967151","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"019745aa-29a9-72bb-891d-fe346d42e5e6","tenant_id":"019745aa-29a8-7335-b944-cb1ae9f11a13","business_name":"Golden Spoon Restaurant","business_type":"food","subdomain":"fresh-mart","slug":"fresh-mart","description":"Fine dining Nigerian restaurant with elegant ambiance and exceptional service.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC938758","tax_identification_number":"50287245-8854","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1610,"max_order_value":22819,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.watsica.com\/cupiditate-nulla-harum-minima.html","tax_clearance":"http:\/\/becker.com\/fugiat-et-delectus-ut-omnis-ut.html","business_permit":"https:\/\/www.prohaska.com\/odio-dignissimos-ut-quasi-nemo-fuga-minus-et","owner_id":"http:\/\/www.durgan.info\/quia-possimus-deleniti-ut-consequatur-et-aliquid.html","bank_statement":"http:\/\/klein.com\/laboriosam-nemo-unde-reiciendis-expedita-assumenda.html"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29aa-739d-98fc-7f7344832896","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29aa-739d-98fc-7f7344832896","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"019745aa-29a5-7198-ab2b-f19d88fd6315","tenant_id":"019745aa-29a4-7224-a787-91b4c1986695","business_name":"Tasty Bites","business_type":"food","subdomain":"lagos-digital-store","slug":"lagos-digital-store","description":"Quick service restaurant offering Nigerian fast food and continental dishes.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC066547","tax_identification_number":"68829720-0938","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1554,"max_order_value":22796,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.mcdermott.com\/mollitia-dolores-hic-qui-id-blanditiis-accusamus-vel","tax_clearance":"https:\/\/www.mcdermott.org\/minima-doloremque-velit-vitae-repellat-quia","business_permit":null,"owner_id":"http:\/\/prosacco.info\/illo-quasi-fuga-temporibus-delectus-culpa-ab","bank_statement":"http:\/\/okuneva.org\/"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29a6-7200-a2dd-d1de95f811b2","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29a6-7200-a2dd-d1de95f811b2","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"019745aa-29b9-7177-9d74-2cd711846db6","tenant_id":"019745aa-29b8-71b1-9ae9-0819d2441705","business_name":"Family Store","business_type":"retail","subdomain":"nigerian-mart","slug":"nigerian-mart","description":"Family-owned store serving the community with quality products at affordable prices.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0011dd?text=business+est","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC985325","tax_identification_number":"11934244-0656","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":857,"max_order_value":27268,"verified_customers_only":false,"business_hours_only":false},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/powlowski.biz\/consequatur-dolore-velit-numquam-qui","tax_clearance":"http:\/\/kub.com\/incidunt-adipisci-aut-qui-minima-voluptas-voluptatibus-aut","business_permit":null,"owner_id":"https:\/\/www.ferry.biz\/et-et-cum-dolore-porro-cumque-quis-sunt","bank_statement":null},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29bb-736a-b509-e0b4e1a774dc","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29bb-736a-b509-e0b4e1a774dc","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"019745aa-29ef-71c5-b874-7f39eb106d9c","tenant_id":"019745aa-29ed-701c-815e-c2757c23556c","business_name":"Home Care Solutions","business_type":"service","subdomain":"golden-tech-hub","slug":"golden-tech-hub","description":"Comprehensive home care and cleaning services for busy families.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"active","cac_registration_number":"BN806676","tax_identification_number":"48387871-0383","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":703,"max_order_value":19191,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.hudson.org\/illo-iure-nemo-consequatur-quia-saepe","tax_clearance":"http:\/\/www.fahey.net\/","business_permit":null,"owner_id":"http:\/\/www.williamson.com\/explicabo-minus-est-aut-animi-nulla-dicta","bank_statement":null},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29f0-730c-ad36-483b6dc19d24","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29f0-730c-ad36-483b6dc19d24","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"019745aa-29be-71ea-a143-82f3c782211d","tenant_id":"019745aa-29bc-73c1-9d02-471d5a0fb11e","business_name":"Express Supermarket","business_type":"retail","subdomain":"royal-kitchen","slug":"royal-kitchen","description":"Modern supermarket with a wide range of local and international products.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0011cc?text=business+non","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN604208","tax_identification_number":"58253136-6096","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1054,"max_order_value":24920,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":false,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/treutel.com\/quia-dolores-ab-qui-aut-sit-quo.html","tax_clearance":"http:\/\/nienow.com\/doloremque-sed-et-sit-omnis-eveniet","business_permit":"http:\/\/www.prohaska.org\/ex-nesciunt-quidem-fuga-quia-quasi-omnis","owner_id":null,"bank_statement":"http:\/\/www.pagac.com\/velit-et-illum-sunt-aut-earum-non-ut.html"},"country_id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","state_id":"019745aa-23de-70ed-99d8-7874f240ef6f","primary_address_id":"019745aa-29bf-73cc-9e3f-f1d0d3bbc7c7","current_subscription_id":null,"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z","primary_address":{"id":"019745aa-29bf-73cc-9e3f-f1d0d3bbc7c7","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false}],"featured_providers":[{"id":"019745aa-2aaa-7209-8a01-6d0838e2ed70","tenant_id":"019745aa-2aa6-7300-b5b7-b6c1f7eaed76","company_name":"Swift Delivery Nigeria","description":"Fast and reliable delivery service across Nigeria","contact_email":"<EMAIL>","contact_phone":"08012345700","status":"verified","is_internal_provider":false,"performance_rating_avg":4.06,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:29.000000Z","updated_at":"2025-06-06T14:34:29.000000Z"},{"id":"019745aa-57ba-7121-94bc-958718aa9503","tenant_id":"019745aa-2990-7249-a8c0-dad8386a4059","company_name":"Jollof Palace Delivery","description":"Internal delivery service for Jollof Palace","contact_email":"<EMAIL>","contact_phone":"07063868544","status":"active","is_internal_provider":true,"performance_rating_avg":4.54,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:40.000000Z","updated_at":"2025-06-06T14:34:40.000000Z"},{"id":"019745aa-4089-72df-ad3c-3334bece3110","tenant_id":"019745aa-4084-73ab-a15c-20267087b396","company_name":"Port Harcourt Delivery Hub","description":"Reliable delivery solutions for Rivers State","contact_email":"<EMAIL>","contact_phone":"08012345703","status":"verified","is_internal_provider":false,"performance_rating_avg":3.7,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:35.000000Z","updated_at":"2025-06-06T14:34:35.000000Z"},{"id":"019745aa-5b60-72ee-b77e-fa6a5e6b9a17","tenant_id":"019745aa-2994-71f4-b550-642f64eb5c4a","company_name":"Suya King Delivery","description":"Internal delivery service for Suya King","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","is_internal_provider":true,"performance_rating_avg":4.99,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:41.000000Z","updated_at":"2025-06-06T14:34:41.000000Z"},{"id":"019745aa-38cd-71b0-b165-5442bdf2519d","tenant_id":"019745aa-38c8-72df-adab-f878d96bfc9c","company_name":"Abuja Quick Dispatch","description":"Same-day delivery service in Abuja and FCT","contact_email":"<EMAIL>","contact_phone":"08012345702","status":"verified","is_internal_provider":false,"performance_rating_avg":4.59,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:33.000000Z","updated_at":"2025-06-06T14:34:33.000000Z"},{"id":"019745aa-564e-7053-b70b-344d6012059a","tenant_id":"019745aa-2981-73b1-8eec-3d9b73e15998","company_name":"Mama Cass Kitchen Delivery","description":"Internal delivery service for Mama Cass Kitchen","contact_email":"<EMAIL>","contact_phone":"***********","status":"active","is_internal_provider":true,"performance_rating_avg":4.22,"country":{"id":"019745aa-23dc-72b8-9c4a-4595ce1d7b24","name":"Nigeria","code":"NG"},"state":{"id":"019745aa-23de-70ed-99d8-7874f240ef6f","name":"Lagos","code":"LA"},"created_at":"2025-06-06T14:34:40.000000Z","updated_at":"2025-06-06T14:34:40.000000Z"}],"statistics":{"total_businesses":22,"total_providers":8,"total_orders_completed":0,"cities_served":1},"testimonials":[{"id":1,"customer_name":"John Doe","business_name":"Pizza Palace","rating":5,"comment":"Amazing service and fast delivery!","created_at":"2025-06-04T17:54:14.496240Z"},{"id":2,"customer_name":"Jane Smith","business_name":"Quick Logistics","rating":5,"comment":"Professional delivery service, highly recommended!","created_at":"2025-06-01T17:54:14.496282Z"}]},"request_id":"c91a1108-df8e-47f3-88b2-f4c59c54c371"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c91a1108-df8e-47f3-88b2-f4c59c54c371
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-aoyAqg/WhvFY871f8vq6fQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/logout-all
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout from all devices.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get authenticated user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.507531Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ca0d4fed-bf4c-4a0f-8aae-0f8c397f9889
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-TxsCV/R2FFVyXImSzPQg1g==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/email/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send email verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/phone/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send phone verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/email/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify email with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your email. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/phone/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify phone with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your phone. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/tenants
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's accessible tenants."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.514384Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b35735cf-58f3-48d5-9261-d8b226103603
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-S45/b4t6C5/SAChedbNKEw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/auth/tenants/{tenantId}/access'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if user can interact with a specific tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenantId:
        name: tenantId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenantId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.519201Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: efb01258-b492-4a9a-ab78-b72159cff80e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-eq8K9GXssKCUuc3rUZg9lA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get current user's profile."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.531493Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ff53c371-5c1e-4f5e-bf39-d2c2553ad196
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-5WBJKNU4O/leZWGgR1zhMg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update user's profile information."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "User's first name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "User's last name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: "User's email address. Must be a valid email address. Must not be greater than 255 characters."
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'User''s phone number in Nigerian format. Must match the regex /^\+234[0-9]{10}$/. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: "User's timezone. Must be a valid time zone, such as <code>Africa/Accra</code>. Must not be greater than 50 characters."
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      avatar_url:
        name: avatar_url
        description: "URL to user's avatar image. Must be a valid URL. Must not be greater than 500 characters."
        required: false
        example: 'https://example.com/avatar.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      bio:
        name: bio
        description: "User's biography. Must not be greater than 1000 characters."
        required: false
        example: 'Software developer passionate about technology'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_of_birth:
        name: date_of_birth
        description: "User's date of birth. Must be a valid date. Must be a date before <code>today</code>."
        required: false
        example: '1990-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      gender:
        name: gender
        description: "User's gender."
        required: false
        example: male
        type: string
        enumValues:
          - male
          - female
          - other
          - prefer_not_to_say
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.street:
        name: address.street
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.city_id:
        name: address.city_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state_id:
        name: address.state_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: c90237e9-ced5-3af6-88ea-84aeaa148878
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.country_id:
        name: address.country_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.is_default:
        name: address.is_default
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '+*************'
      timezone: Africa/Lagos
      avatar_url: 'https://example.com/avatar.jpg'
      bio: 'Software developer passionate about technology'
      date_of_birth: '1990-01-15'
      gender: male
      address:
        street: b
        city_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
        state_id: c90237e9-ced5-3af6-88ea-84aeaa148878
        country_id: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        postal_code: vdljnikhwaykcmyu
        is_default: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/user/change-password
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Change user's password."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_password:
        name: current_password
        description: 'Current password for verification.'
        required: true
        example: CurrentPassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      new_password:
        name: new_password
        description: 'New password (minimum 8 characters, must contain uppercase, lowercase, numbers, and symbols).'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      current_password: CurrentPassword123!
      new_password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.540110Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: fc1cf7d2-7906-43dc-a5d3-4a44cf3cd4db
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-1O/YB9+9+OmM7+S5NCh9fQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update user's preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      notifications:
        name: notifications
        description: 'Notification preferences.'
        required: false
        example:
          email_enabled: true
          sms_enabled: false
          push_enabled: true
          marketing_enabled: false
          order_updates: true
          delivery_updates: true
          promotional_offers: false
          security_alerts: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.email_enabled:
        name: notifications.email_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.sms_enabled:
        name: notifications.sms_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.push_enabled:
        name: notifications.push_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.marketing_enabled:
        name: notifications.marketing_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.order_updates:
        name: notifications.order_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.delivery_updates:
        name: notifications.delivery_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.promotional_offers:
        name: notifications.promotional_offers
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.security_alerts:
        name: notifications.security_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication:
        name: communication
        description: 'Communication preferences.'
        required: false
        example:
          preferred_language: en
          preferred_contact_method: email
          contact_time_start: '09:00'
          contact_time_end: '18:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_language:
        name: communication.preferred_language
        description: ''
        required: false
        example: en
        type: string
        enumValues:
          - en
          - yo
          - ig
          - ha
          - fr
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_contact_method:
        name: communication.preferred_contact_method
        description: ''
        required: false
        example: email
        type: string
        enumValues:
          - email
          - sms
          - phone
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_start:
        name: communication.contact_time_start
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_end:
        name: communication.contact_time_end
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '18:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy:
        name: privacy
        description: 'Privacy preferences.'
        required: false
        example:
          profile_visibility: private
          location_sharing: false
          activity_tracking: true
          data_analytics: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.profile_visibility:
        name: privacy.profile_visibility
        description: ''
        required: false
        example: private
        type: string
        enumValues:
          - public
          - private
          - contacts_only
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.location_sharing:
        name: privacy.location_sharing
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.activity_tracking:
        name: privacy.activity_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.data_analytics:
        name: privacy.data_analytics
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app:
        name: app
        description: 'Application preferences.'
        required: false
        example:
          theme: auto
          currency: NGN
          distance_unit: km
          temperature_unit: celsius
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.theme:
        name: app.theme
        description: ''
        required: false
        example: auto
        type: string
        enumValues:
          - light
          - dark
          - auto
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.currency:
        name: app.currency
        description: ''
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
          - EUR
          - GBP
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.distance_unit:
        name: app.distance_unit
        description: ''
        required: false
        example: km
        type: string
        enumValues:
          - km
          - miles
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.temperature_unit:
        name: app.temperature_unit
        description: ''
        required: false
        example: celsius
        type: string
        enumValues:
          - celsius
          - fahrenheit
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      notifications:
        email_enabled: true
        sms_enabled: false
        push_enabled: true
        marketing_enabled: false
        order_updates: true
        delivery_updates: true
        promotional_offers: false
        security_alerts: true
      communication:
        preferred_language: en
        preferred_contact_method: email
        contact_time_start: '09:00'
        contact_time_end: '18:00'
      privacy:
        profile_visibility: private
        location_sharing: false
        activity_tracking: true
        data_analytics: true
      app:
        theme: auto
        currency: NGN
        distance_unit: km
        temperature_unit: celsius
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/devices
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's active devices/sessions."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.546532Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 386ad94f-43f7-4b27-92f9-c9a6b82d8ae8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-zUL0F+Y9SsjS4pwB+Rtm+Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/user/deactivate
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Deactivate user's account."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      reason:
        name: reason
        description: 'Must not be greater than 500 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      password:
        name: password
        description: ''
        required: true
        example: '|]|{+-'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      reason: b
      password: '|]|{+-'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's notifications with pagination and filtering."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.551991Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8e46adca-72d1-45d0-aa53-1c3d3cd25def
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-mnDtc34XcK035CqrDLrAug==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/stats
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get notification statistics.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.555856Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 909c6c64-61cf-4844-b39e-4ef6c7ad6e66
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-dFFtgE0oByhA7N6sov97qw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/notifications/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific notification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.559295Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8e57e42d-48ab-4d6c-858f-dc31d717dced
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-cX/lJqoMJLxHVpMLC+FnsA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/notifications/{id}/read'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark notification as read.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/notifications/mark-all-read
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark all notifications as read.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/notifications/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete notification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register FCM token for push notifications.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      token:
        name: token
        description: 'Must be at least 10 characters.'
        required: true
        example: bngzmiyvdljnikhwaykcmyuwpwlvqwrsitcpscqldzs
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      platform:
        name: platform
        description: ''
        required: true
        example: android
        type: string
        enumValues:
          - web
          - android
          - ios
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      token: bngzmiyvdljnikhwaykcmyuwpwlvqwrsitcpscqldzs
      platform: android
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's FCM tokens."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.564105Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b38493b4-95b8-47f8-b695-82d1d7bb272b
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-4RcXrn4pQ2tn92FxBE4Hbg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove FCM token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      token:
        name: token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      token: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's notification preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.567829Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 89cbd861-5b40-4598-9648-9d6da7e7fd46
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-QeYtmsm25D49L40NN1byFw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/notifications/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update user's notification preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      channel:
        name: channel
        description: ''
        required: true
        example: sms
        type: string
        enumValues:
          - push
          - sms
          - email
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: order_status
        type: string
        enumValues:
          - order_status
          - delivery_update
          - payment_confirmation
          - promotional
          - system_announcement
        exampleWasSpecified: false
        nullable: false
        custom: []
      enabled:
        name: enabled
        description: ''
        required: true
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      channel: sms
      type: order_status
      enabled: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/notifications/preferences/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset notification preferences to defaults.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register/admin
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register platform admin (super admin only).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: 'First name of the admin user. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name of the admin user. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the admin account. Must be a valid email address.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the admin account (minimum 8 characters). Must be at least 8 characters.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Phone number for the admin (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      role:
        name: role
        description: 'Admin role - "platform-admin" or "super-admin".'
        required: true
        example: platform-admin
        type: string
        enumValues:
          - platform-admin
          - super-admin
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      role: platform-admin
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get tenant statistics for admin dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.595719Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: fba70686-746b-4363-b1da-9a9194d5fd66
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-sz7rGFVAet0mlLq1kseqfQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants/check-subdomain
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if subdomain is available.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.601790Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8625c084-df9e-47d0-a649-6dd10a687822
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-+cF5IJT0e7Fd8LuwcVrxJA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/tenants/{tenant}/restore'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restore an archived tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenant:
        name: tenant
        description: 'The tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenant: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/tenants/{tenant}/status'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update tenant status.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenant:
        name: tenant
        description: 'The tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenant: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      status:
        name: status
        description: 'New status for the tenant - "active", "inactive", "suspended", "pending_verification".'
        required: true
        example: active
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for the status change (max 500 characters). Must not be greater than 500 characters.'
        required: false
        example: 'Tenant has completed verification requirements'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      status: active
      reason: 'Tenant has completed verification requirements'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/tenants
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Name of the tenant (business or delivery provider). Must match the regex /^[a-zA-Z0-9\s\-_&.]+$/. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Type of tenant - "business" or "delivery_provider".'
        required: true
        example: business
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: 'Initial status of the tenant (optional, defaults to pending_verification).'
        required: false
        example: pending_verification
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      subscription_plan_id:
        name: subscription_plan_id
        description: 'ID of the subscription plan to assign (optional). Must be a valid UUID. The <code>id</code> of an existing record in the subscription_plans table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Custom subdomain for the tenant (optional, auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must be at least 3 characters. Must not be greater than 30 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: 'Tenant settings configuration (optional).'
        required: false
        example:
          features:
            api_access_enabled: true
            webhook_enabled: false
            real_time_tracking: true
          limits:
            max_orders_per_month: 1000
            max_api_calls_per_day: 10000
            max_storage_mb: 1000
          security:
            two_factor_required: false
            session_timeout_minutes: 60
            password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.features:
        name: settings.features
        description: ''
        required: false
        example:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.api_access_enabled:
        name: settings.features.api_access_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.webhook_enabled:
        name: settings.features.webhook_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.real_time_tracking:
        name: settings.features.real_time_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits:
        name: settings.limits
        description: ''
        required: false
        example:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_orders_per_month:
        name: settings.limits.max_orders_per_month
        description: 'Must be at least 1. Must not be greater than 1000000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_api_calls_per_day:
        name: settings.limits.max_api_calls_per_day
        description: 'Must be at least 100. Must not be greater than 10000000.'
        required: false
        example: 10000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_storage_mb:
        name: settings.limits.max_storage_mb
        description: 'Must be at least 100. Must not be greater than 100000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security:
        name: settings.security
        description: ''
        required: false
        example:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.two_factor_required:
        name: settings.security.two_factor_required
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.session_timeout_minutes:
        name: settings.security.session_timeout_minutes
        description: 'Must be at least 15. Must not be greater than 1440.'
        required: false
        example: 60
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.password_expiry_days:
        name: settings.security.password_expiry_days
        description: 'Must be at least 30. Must not be greater than 365.'
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Delicious Eats Restaurant'
      tenant_type: business
      status: pending_verification
      subscription_plan_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      subdomain: delicious-eats
      settings:
        features:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        limits:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        security:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.612449Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 6c6782cc-94ec-4ecf-9f10-fadf61587440
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-xUcX4cAevGaaKfbcE8l42Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Name of the tenant (business or delivery provider). Must match the regex /^[a-zA-Z0-9\s\-_&.]+$/. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: false
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Type of tenant - "business" or "delivery_provider".'
        required: false
        example: business
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: 'Status of the tenant.'
        required: false
        example: active
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      subscription_plan_id:
        name: subscription_plan_id
        description: 'ID of the subscription plan to assign. Must be a valid UUID. The <code>id</code> of an existing record in the subscription_plans table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: 'Tenant settings configuration.'
        required: false
        example:
          features:
            api_access_enabled: true
            webhook_enabled: false
            real_time_tracking: true
          limits:
            max_orders_per_month: 1000
            max_api_calls_per_day: 10000
            max_storage_mb: 1000
          security:
            two_factor_required: false
            session_timeout_minutes: 60
            password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.features:
        name: settings.features
        description: ''
        required: false
        example:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.api_access_enabled:
        name: settings.features.api_access_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.webhook_enabled:
        name: settings.features.webhook_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.real_time_tracking:
        name: settings.features.real_time_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits:
        name: settings.limits
        description: ''
        required: false
        example:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_orders_per_month:
        name: settings.limits.max_orders_per_month
        description: 'Must be at least 1. Must not be greater than 1000000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_api_calls_per_day:
        name: settings.limits.max_api_calls_per_day
        description: 'Must be at least 100. Must not be greater than 10000000.'
        required: false
        example: 10000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_storage_mb:
        name: settings.limits.max_storage_mb
        description: 'Must be at least 100. Must not be greater than 100000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security:
        name: settings.security
        description: ''
        required: false
        example:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.two_factor_required:
        name: settings.security.two_factor_required
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.session_timeout_minutes:
        name: settings.security.session_timeout_minutes
        description: 'Must be at least 15. Must not be greater than 1440.'
        required: false
        example: 60
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.password_expiry_days:
        name: settings.security.password_expiry_days
        description: 'Must be at least 30. Must not be greater than 365.'
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Delicious Eats Restaurant'
      tenant_type: business
      status: active
      subscription_plan_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      settings:
        features:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        limits:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        security:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Archive the specified tenant (soft delete).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of businesses.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.627065Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 3bf72ad0-8e7d-4f17-978b-dbd009b488a6
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-KOsW3lcH1c+EZ0TaoN1zmw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description. Must not be greater than 1000 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business (auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business (auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Business contact email. Must be a valid email address. Must not be greater than 255 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Business contact phone number. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'Whether to automatically accept orders globally.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting orders.'
        required: false
        example:
          min_value: 500
          max_distance: 5
          business_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'Business operating hours for each day.'
        required: false
        example:
          monday:
            open: '09:00'
            close: '22:00'
          tuesday:
            open: '09:00'
            close: '22:00'
          wednesday:
            open: '09:00'
            close: '22:00'
          thursday:
            open: '09:00'
            close: '22:00'
          friday:
            open: '09:00'
            close: '23:00'
          saturday:
            open: '10:00'
            close: '23:00'
          sunday:
            open: '12:00'
            close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: 'Business address information.'
        required: false
        example:
          street: '123 Main Street'
          city: Lagos
          state: 'Lagos State'
          postal_code: '100001'
          country: Nigeria
          latitude: 6.5244
          longitude: 3.3792
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.address_line_1:
        name: address.address_line_1
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.address_line_2:
        name: address.address_line_2
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.city:
        name: address.city
        description: 'This field is required when <code>address</code> is present. Must not be greater than 100 characters.'
        required: false
        example: Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state:
        name: address.state
        description: 'This field is required when <code>address</code> is present. Must not be greater than 100 characters.'
        required: false
        example: 'Lagos State'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: '100001'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.latitude:
        name: address.latitude
        description: 'Must be between -90 and 90.'
        required: false
        example: 6.5244
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.longitude:
        name: address.longitude
        description: 'Must be between -180 and 180.'
        required: false
        example: 3.3792
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      admin_user_id:
        name: admin_user_id
        description: 'ID of the user to assign as business admin. Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: false
        example: c3d4e5f6-g7h8-9012-cdef-g34567890123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: <EMAIL>
      contact_phone: '+*************'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500
        max_distance: 5
        business_hours_only: true
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      address:
        street: '123 Main Street'
        city: Lagos
        state: 'Lagos State'
        postal_code: '100001'
        country: Nigeria
        latitude: 6.5244
        longitude: 3.3792
        address_line_1: b
        address_line_2: 'n'
      admin_user_id: c3d4e5f6-g7h8-9012-cdef-g34567890123
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.637003Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 26d5b798-a4c2-4a62-9ffd-117ee6d22642
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-tyQcyXSK76QU0b90dbTITg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: false
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: false
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description. Must not be greater than 1000 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_email:
        name: contact_email
        description: 'Business contact email. Must be a valid email address. Must not be greater than 255 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Business contact phone number. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'Whether to automatically accept orders globally.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting orders.'
        required: false
        example:
          min_value: 500
          max_distance: 5
          business_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'Business operating hours for each day.'
        required: false
        example:
          monday:
            open: '09:00'
            close: '22:00'
          tuesday:
            open: '09:00'
            close: '22:00'
          wednesday:
            open: '09:00'
            close: '22:00'
          thursday:
            open: '09:00'
            close: '22:00'
          friday:
            open: '09:00'
            close: '23:00'
          saturday:
            open: '10:00'
            close: '23:00'
          sunday:
            open: '12:00'
            close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      logo:
        name: logo
        description: 'Business logo image file (JPEG, PNG, JPG, GIF, or SVG, max 2MB). Must be an image. Must not be greater than 2048 kilobytes.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      verification_documents:
        name: verification_documents
        description: 'Must not be greater than 500 characters.'
        required: false
        example:
          - b
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: <EMAIL>
      contact_phone: '+*************'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500
        max_distance: 5
        business_hours_only: true
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      verification_documents:
        - b
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified business (soft delete).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/restore'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restore an archived business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/activate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate a business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/suspend'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Suspend a business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/verify'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark business as verified.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/businesses/{business}/admins'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business admins.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.649768Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8378dd79-35a4-404d-83c7-9d2aaed9b0ce
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-euOYihF1i6blZaKt3mbbUQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/assign-admin'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assign admin to business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      user_id:
        name: user_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: ''
        required: true
        example: business-owner
        type: string
        enumValues:
          - business-admin
          - business-manager
          - business-owner
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      user_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
      role: business-owner
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/transfer-admin'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Transfer business ownership.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      new_owner_id:
        name: new_owner_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      new_owner_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/businesses/{business}/admins/{user}/role'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update admin role.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      role:
        name: role
        description: ''
        required: true
        example: business-admin
        type: string
        enumValues:
          - business-admin
          - business-manager
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      role: business-admin
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/businesses/{business}/admins/{user}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove admin from business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Browse businesses (customer discovery).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.719719Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 31c28745-45bd-4052-b6d2-a547857dad18
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-fwIsWRv8kelhDbgiH368cg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Show business details for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.724571Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: e058f66d-601b-4b1c-b42a-678afadb4338
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-lY/rViufyrkEOyyoS6qv0g==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}/products'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business products for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.732723Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 79a73dd8-083c-4669-8847-0fabcfc4efd9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-zjkUY6zssdq1P6F3nRhbXg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}/menu'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business menu (organized by categories).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.737342Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0e68c18c-75e1-4624-9d0e-c10b223b5a5e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-zkWYKHE8AxUog3fD4wPZgw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/providers
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Browse providers (customer discovery).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.742763Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 85141083-9f23-4580-9a48-42df5032e9e6
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-FdDgrphGQG8ywGB/Ky/aGA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/providers/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Show provider details for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.746780Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 055e0c99-7cd9-407b-b0ce-e4b67bbbf511
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-QGktN8mxNk6m7nIrrmIg/Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/providers/{id}/service-areas'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider service areas for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-06T17:54:14.751005Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c96e6c7a-8f5b-4537-8783-8cc38c0b92a8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-vM+DSGQk06kTB4tBVBoOlg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://api.deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/providers/{id}/check-availability'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if provider serves a specific location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      latitude:
        name: latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      latitude: -89
      longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
