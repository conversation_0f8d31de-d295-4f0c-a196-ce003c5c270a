name: Webhooks
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/webhooks/paystack
    metadata:
      groupName: Webhooks
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Handle Paystack webhook.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/webhooks/plivo/sms-status
    metadata:
      groupName: Webhooks
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Handle Plivo SMS status webhook.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/webhooks/twilio/sms-status
    metadata:
      groupName: Webhooks
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Handle Twilio SMS status webhook.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/webhooks/test
    metadata:
      groupName: Webhooks
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Handle test webhook (for development).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
