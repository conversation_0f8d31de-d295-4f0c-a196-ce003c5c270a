name: 'Admin User Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/users
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all users across tenants.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search users by name or email.'
        required: false
        example: john
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      user_type:
        name: user_type
        description: 'Filter by user type.'
        required: false
        example: business_owner
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tenant_id:
        name: tenant_id
        description: 'Filter by tenant ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Filter by active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email_verified:
        name: email_verified
        description: 'Filter by email verification.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field.'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: john
      user_type: business_owner
      tenant_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      is_active: true
      email_verified: true
      sort_by: created_at
      sort_direction: desc
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Users retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "first_name": "John",
                  "last_name": "Doe",
                  "email": "<EMAIL>",
                  "phone_number": "***********",
                  "user_type": "business_owner",
                  "is_active": true,
                  "email_verified_at": "2024-01-15T10:30:00Z",
                  "phone_verified_at": "2024-01-15T10:30:00Z",
                  "tenant": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                    "name": "Example Business"
                  },
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 150
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/users/{id}'
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific user details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 'User ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      user: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "User retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "first_name": "John",
              "last_name": "Doe",
              "email": "<EMAIL>",
              "phone_number": "***********",
              "user_type": "business_owner",
              "is_active": true,
              "email_verified_at": "2024-01-15T10:30:00Z",
              "phone_verified_at": "2024-01-15T10:30:00Z",
              "tenant": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "name": "Example Business",
                "tenant_type": "business"
              },
              "roles": ["business-owner"],
              "abilities": ["manage-business"],
              "addresses": [],
              "created_at": "2024-01-15T10:30:00Z",
              "updated_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/users/{id}'
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 'User ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      user: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "User's first name."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "User's last name."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email:
        name: email
        description: "User's email address."
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: "User's phone number."
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: "User's active status."
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '***********'
      is_active: true
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "User updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "first_name": "John",
              "last_name": "Doe",
              "email": "<EMAIL>",
              "phone_number": "***********",
              "is_active": true
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/users/{user}/activate'
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate a user.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: 'User ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "User activated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "is_active": true
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/users/{user}/suspend'
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Suspend a user.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: 'User ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "User suspended successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "is_active": false
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/users/{user}/verify-email'
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Verify user's email."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: 'User ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "User email verified successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "email_verified_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/users/{user}/verify-phone'
    metadata:
      groupName: 'Admin User Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Verify user's phone."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: 'User ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "User phone verified successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "phone_verified_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
