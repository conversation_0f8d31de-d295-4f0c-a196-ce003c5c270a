name: 'Admin Tenant Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants
    metadata:
      groupName: 'Admin Tenant Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a paginated listing of tenants with filtering.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search tenants by name.'
        required: false
        example: restaurant
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by status.'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Filter by tenant type.'
        required: false
        example: business
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      created_by:
        name: created_by
        description: 'Filter by creator ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field.'
        required: false
        example: name
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction.'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: restaurant
      status: active
      tenant_type: business
      created_by: 019723aa-3202-70dd-a0c1-3565681dd87a
      sort_by: name
      sort_direction: asc
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Tenants retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Restaurant Chain Ltd",
                  "tenant_type": "business",
                  "status": "active",
                  "subscription_plan": "premium",
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 50
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
