name: 'Customer Deliveries'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/customer/deliveries
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of customer deliveries.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Number of items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search in delivery reference, tracking ID.'
        required: false
        example: DEL-2024
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by delivery status.'
        required: false
        example: pending
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      type:
        name: type
        description: 'Filter by delivery type (order, adhoc).'
        required: false
        example: order
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Filter deliveries from date (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Filter deliveries to date (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (created_at, status).'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: DEL-2024
      status: pending
      type: order
      date_from: '2024-01-01'
      date_to: '2024-01-31'
      sort_by: created_at
      sort_direction: desc
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: false
        example: order
        type: string
        enumValues:
          - order
          - adhoc
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-06'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-06-30'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: status
        type: string
        enumValues:
          - created_at
          - status
          - estimated_delivery_time
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: ''
        required: false
        example: asc
        type: string
        enumValues:
          - asc
          - desc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      status: architecto
      type: order
      date_from: '2025-06-06'
      date_to: '2051-06-30'
      sort_by: status
      sort_direction: asc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Deliveries retrieved successfully",
            "data": {
              "deliveries": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "tracking_id": "TRK-2024-001",
                  "type": "order",
                  "status": "pending",
                  "order_reference": "ORD-2024-001",
                  "delivery_provider": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                    "name": "FastDelivery Co"
                  },
                  "estimated_delivery_time": "2024-01-15T12:30:00Z",
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "pagination": {
                "current_page": 1,
                "per_page": 15,
                "total": 50,
                "last_page": 4
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/customer/deliveries
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a new delivery request.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      pickup_address:
        name: pickup_address
        description: 'Pickup address details.'
        required: true
        example:
          street: '123 Main St'
          city: Lagos
          state: Lagos
          country: Nigeria
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      delivery_address:
        name: delivery_address
        description: 'Delivery address details.'
        required: true
        example:
          street: '456 Customer Ave'
          city: Lagos
          state: Lagos
          country: Nigeria
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      item_description:
        name: item_description
        description: 'Description of items to deliver.'
        required: true
        example: 'Electronics package'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      item_weight_kg:
        name: item_weight_kg
        description: 'Weight of items in kg.'
        required: true
        example: 2.5
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      delivery_instructions:
        name: delivery_instructions
        description: 'Instructions for delivery.'
        required: false
        example: 'Call on arrival'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      preferred_delivery_time:
        name: preferred_delivery_time
        description: 'Preferred delivery time.'
        required: false
        example: '2024-01-15T14:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      pickup_address:
        street: '123 Main St'
        city: Lagos
        state: Lagos
        country: Nigeria
      delivery_address:
        street: '456 Customer Ave'
        city: Lagos
        state: Lagos
        country: Nigeria
      item_description: 'Electronics package'
      item_weight_kg: 2.5
      delivery_instructions: 'Call on arrival'
      preferred_delivery_time: '2024-01-15T14:00:00Z'
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Delivery request created successfully",
            "data": {
              "delivery": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "delivery_reference": "DEL-2024-001",
                "status": "pending",
                "estimated_cost": 1500.00
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/deliveries/{id}'
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified customer delivery.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters:
      type:
        name: type
        description: 'Delivery type (order, adhoc).'
        required: false
        example: order
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      type: order
    bodyParameters:
      type:
        name: type
        description: ''
        required: false
        example: adhoc
        type: string
        enumValues:
          - order
          - adhoc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      type: adhoc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery retrieved successfully",
            "data": {
              "delivery": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "tracking_id": "TRK-2024-001",
                "type": "order",
                "status": "pending",
                "order": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "order_reference": "ORD-2024-001",
                  "business_name": "Pizza Palace"
                },
                "delivery_provider": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "name": "FastDelivery Co",
                  "phone": "+234-************"
                },
                "pickup_address": {
                  "address_line_1": "123 Business Street",
                  "city": "Lagos",
                  "state": "Lagos"
                },
                "delivery_address": {
                  "address_line_1": "456 Customer Street",
                  "city": "Lagos",
                  "state": "Lagos"
                },
                "estimated_pickup_time": "2024-01-15T11:00:00Z",
                "estimated_delivery_time": "2024-01-15T12:30:00Z",
                "actual_pickup_time": null,
                "actual_delivery_time": null,
                "created_at": "2024-01-15T10:30:00Z"
              }
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 404
        content: |-
          {
            "success": false,
            "message": "Delivery not found"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/customer/deliveries/request
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Request a new delivery (alias for store).'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/deliveries/{delivery}/cancel'
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel a delivery.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery:
        name: delivery
        description: 'The delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      reason:
        name: reason
        description: 'Reason for cancellation.'
        required: false
        example: 'Changed plans'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      reason: 'Changed plans'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery cancelled successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/deliveries/{delivery}/track'
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Track a delivery.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery:
        name: delivery
        description: 'The delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery tracking retrieved successfully",
            "data": {
              "tracking": {
                "status": "in_transit",
                "current_location": {
                  "lat": 6.5244,
                  "lng": 3.3792
                },
                "estimated_arrival": "2024-01-15T14:30:00Z"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/deliveries/{delivery}/rate'
    metadata:
      groupName: 'Customer Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Rate a completed delivery.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery:
        name: delivery
        description: 'The delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rating:
        name: rating
        description: 'Rating from 1 to 5.'
        required: true
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      review:
        name: review
        description: 'Review comment.'
        required: false
        example: 'Excellent service!'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      rating: 5
      review: 'Excellent service!'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery rated successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
