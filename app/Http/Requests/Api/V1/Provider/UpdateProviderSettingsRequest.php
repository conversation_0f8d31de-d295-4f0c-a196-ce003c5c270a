<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1\Provider;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProviderSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Delivery operation settings
            'max_orders_per_batch' => 'sometimes|integer|min:1|max:20',
            'preferred_vehicle_type' => 'sometimes|string|in:bicycle,motorcycle,car,van,truck',
            'max_delivery_distance_km' => 'sometimes|numeric|min:1|max:50',
            'auto_accept_deliveries' => 'sometimes|boolean',
            'delivery_fee_per_km' => 'sometimes|numeric|min:0',
            'base_delivery_fee' => 'sometimes|numeric|min:0',

            // Working hours and breaks
            'working_hours' => 'sometimes|array',
            'working_hours.*.day' => 'required_with:working_hours|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.start_time' => 'required_with:working_hours|date_format:H:i',
            'working_hours.*.end_time' => 'required_with:working_hours|date_format:H:i',
            'working_hours.*.is_available' => 'sometimes|boolean',

            'break_time_start' => 'sometimes|date_format:H:i',
            'break_time_end' => 'sometimes|date_format:H:i',
            'break_duration_minutes' => 'sometimes|integer|min:15|max:120',

            // Service areas
            'service_areas' => 'sometimes|array',
            'service_areas.*.area_name' => 'required_with:service_areas|string|max:255',
            'service_areas.*.delivery_fee' => 'required_with:service_areas|numeric|min:0',
            'service_areas.*.estimated_time_minutes' => 'required_with:service_areas|integer|min:5|max:180',

            // Auto-acceptance criteria
            'auto_acceptance_criteria' => 'sometimes|array',
            'auto_acceptance_criteria.min_delivery_fee' => 'sometimes|numeric|min:0',
            'auto_acceptance_criteria.max_distance_km' => 'sometimes|numeric|min:1|max:50',
            'auto_acceptance_criteria.working_hours_only' => 'sometimes|boolean',
            'auto_acceptance_criteria.exclude_rush_hours' => 'sometimes|boolean',

            // Notification settings
            'notification_settings' => 'sometimes|array',
            'notification_settings.new_delivery_sound' => 'sometimes|boolean',
            'notification_settings.route_optimization_alerts' => 'sometimes|boolean',
            'notification_settings.earnings_summary' => 'sometimes|boolean',
            'notification_settings.maintenance_reminders' => 'sometimes|boolean',

            // Vehicle and equipment settings
            'vehicle_settings' => 'sometimes|array',
            'vehicle_settings.license_plate' => 'sometimes|string|max:20',
            'vehicle_settings.vehicle_model' => 'sometimes|string|max:100',
            'vehicle_settings.vehicle_year' => 'sometimes|integer|min:1990|max:2030',
            'vehicle_settings.insurance_expiry' => 'sometimes|date|after:today',
            'vehicle_settings.has_insulated_bag' => 'sometimes|boolean',
            'vehicle_settings.has_gps_tracker' => 'sometimes|boolean',
        ];
    }

    /**
     * Get the body parameters for API documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'max_orders_per_batch' => [
                'description' => 'Maximum number of orders to handle in one batch',
                'example' => 5,
            ],
            'preferred_vehicle_type' => [
                'description' => 'Preferred vehicle type for deliveries',
                'example' => 'motorcycle',
            ],
            'max_delivery_distance_km' => [
                'description' => 'Maximum delivery distance in kilometers',
                'example' => 25.0,
            ],
            'auto_accept_deliveries' => [
                'description' => 'Automatically accept deliveries that meet criteria',
                'example' => true,
            ],
            'delivery_fee_per_km' => [
                'description' => 'Fee charged per kilometer',
                'example' => 50.00,
            ],
            'base_delivery_fee' => [
                'description' => 'Base delivery fee',
                'example' => 200.00,
            ],
            'working_hours' => [
                'description' => 'Working hours for each day',
                'example' => [
                    [
                        'day' => 'monday',
                        'start_time' => '08:00',
                        'end_time' => '18:00',
                        'is_available' => true,
                    ],
                ],
            ],
            'break_time_start' => [
                'description' => 'Break time start',
                'example' => '12:00',
            ],
            'break_time_end' => [
                'description' => 'Break time end',
                'example' => '13:00',
            ],
            'service_areas' => [
                'description' => 'Service areas and their delivery fees',
                'example' => [
                    [
                        'area_name' => 'Victoria Island',
                        'delivery_fee' => 500.00,
                        'estimated_time_minutes' => 30,
                    ],
                ],
            ],
            'auto_acceptance_criteria' => [
                'description' => 'Criteria for automatically accepting deliveries',
                'example' => [
                    'min_delivery_fee' => 300,
                    'max_distance_km' => 15,
                    'working_hours_only' => true,
                ],
            ],
            'vehicle_settings' => [
                'description' => 'Vehicle and equipment information',
                'example' => [
                    'license_plate' => 'ABC-123-XY',
                    'vehicle_model' => 'Honda CB150R',
                    'vehicle_year' => 2022,
                    'has_insulated_bag' => true,
                ],
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'max_orders_per_batch.min' => 'Maximum orders per batch must be at least 1.',
            'max_orders_per_batch.max' => 'Maximum orders per batch cannot exceed 20.',
            'preferred_vehicle_type.in' => 'Vehicle type must be bicycle, motorcycle, car, van, or truck.',
            'max_delivery_distance_km.min' => 'Maximum delivery distance must be at least 1 km.',
            'max_delivery_distance_km.max' => 'Maximum delivery distance cannot exceed 50 km.',
            'delivery_fee_per_km.min' => 'Delivery fee per km cannot be negative.',
            'base_delivery_fee.min' => 'Base delivery fee cannot be negative.',
            'working_hours.*.day.in' => 'Day must be a valid day of the week.',
            'working_hours.*.start_time.date_format' => 'Start time must be in HH:MM format.',
            'working_hours.*.end_time.date_format' => 'End time must be in HH:MM format.',
            'break_time_start.date_format' => 'Break time start must be in HH:MM format.',
            'break_time_end.date_format' => 'Break time end must be in HH:MM format.',
            'break_duration_minutes.min' => 'Break duration must be at least 15 minutes.',
            'break_duration_minutes.max' => 'Break duration cannot exceed 2 hours.',
            'vehicle_settings.vehicle_year.min' => 'Vehicle year must be 1990 or later.',
            'vehicle_settings.vehicle_year.max' => 'Vehicle year cannot be in the future.',
            'vehicle_settings.insurance_expiry.after' => 'Insurance must not be expired.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate break time range
            if ($this->has('break_time_start') && $this->has('break_time_end')) {
                $start = $this->input('break_time_start');
                $end = $this->input('break_time_end');

                if ($start >= $end) {
                    $validator->errors()->add('break_time_end', 'Break time end must be after start time.');
                }
            }

            // Validate working hours
            if ($this->has('working_hours') && is_array($this->input('working_hours'))) {
                foreach ($this->input('working_hours') as $index => $hours) {
                    if (isset($hours['start_time']) && isset($hours['end_time']) && ($hours['is_available'] ?? true)) {
                        if ($hours['start_time'] >= $hours['end_time']) {
                            $validator->errors()->add(
                                "working_hours.{$index}.end_time",
                                'End time must be after start time.'
                            );
                        }
                    }
                }
            }
        });
    }
}
