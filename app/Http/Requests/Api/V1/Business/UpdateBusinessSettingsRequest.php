<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1\Business;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBusinessSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Order management settings
            'auto_accept_orders' => 'sometimes|boolean',
            'order_notification_sound' => 'sometimes|boolean',
            'delivery_radius_km' => 'sometimes|numeric|min:1|max:100',
            'minimum_order_value' => 'sometimes|numeric|min:0',
            'preparation_time_minutes' => 'sometimes|integer|min:5|max:180',

            // Operating hours
            'operating_hours' => 'sometimes|array',
            'operating_hours.*.day' => 'required_with:operating_hours|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'operating_hours.*.open_time' => 'required_with:operating_hours|date_format:H:i',
            'operating_hours.*.close_time' => 'required_with:operating_hours|date_format:H:i',
            'operating_hours.*.is_closed' => 'sometimes|boolean',

            // Auto-acceptance criteria
            'auto_acceptance_criteria' => 'sometimes|array',
            'auto_acceptance_criteria.min_value' => 'sometimes|numeric|min:0',
            'auto_acceptance_criteria.max_distance' => 'sometimes|numeric|min:1|max:50',
            'auto_acceptance_criteria.business_hours_only' => 'sometimes|boolean',

            // Payment settings
            'payment_settings' => 'sometimes|array',
            'payment_settings.accept_cash' => 'sometimes|boolean',
            'payment_settings.accept_card' => 'sometimes|boolean',
            'payment_settings.accept_transfer' => 'sometimes|boolean',
            'payment_settings.require_payment_before_preparation' => 'sometimes|boolean',

            // Notification settings
            'notification_settings' => 'sometimes|array',
            'notification_settings.new_order_sound' => 'sometimes|boolean',
            'notification_settings.low_stock_alerts' => 'sometimes|boolean',
            'notification_settings.daily_summary' => 'sometimes|boolean',
            'notification_settings.weekly_report' => 'sometimes|boolean',
        ];
    }

    /**
     * Get the body parameters for API documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'auto_accept_orders' => [
                'description' => 'Automatically accept orders that meet criteria',
                'example' => true,
            ],
            'order_notification_sound' => [
                'description' => 'Play sound for new order notifications',
                'example' => true,
            ],
            'delivery_radius_km' => [
                'description' => 'Maximum delivery radius in kilometers',
                'example' => 10.5,
            ],
            'minimum_order_value' => [
                'description' => 'Minimum order value to accept',
                'example' => 500.00,
            ],
            'preparation_time_minutes' => [
                'description' => 'Average preparation time in minutes',
                'example' => 30,
            ],
            'operating_hours' => [
                'description' => 'Business operating hours for each day',
                'example' => [
                    [
                        'day' => 'monday',
                        'open_time' => '09:00',
                        'close_time' => '22:00',
                        'is_closed' => false,
                    ],
                    [
                        'day' => 'sunday',
                        'open_time' => '10:00',
                        'close_time' => '20:00',
                        'is_closed' => false,
                    ],
                ],
            ],
            'auto_acceptance_criteria' => [
                'description' => 'Criteria for automatically accepting orders',
                'example' => [
                    'min_value' => 500,
                    'max_distance' => 5,
                    'business_hours_only' => true,
                ],
            ],
            'payment_settings' => [
                'description' => 'Payment method preferences',
                'example' => [
                    'accept_cash' => true,
                    'accept_card' => true,
                    'accept_transfer' => true,
                    'require_payment_before_preparation' => false,
                ],
            ],
            'notification_settings' => [
                'description' => 'Business notification preferences',
                'example' => [
                    'new_order_sound' => true,
                    'low_stock_alerts' => true,
                    'daily_summary' => true,
                    'weekly_report' => false,
                ],
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'delivery_radius_km.min' => 'Delivery radius must be at least 1 km.',
            'delivery_radius_km.max' => 'Delivery radius cannot exceed 100 km.',
            'minimum_order_value.min' => 'Minimum order value cannot be negative.',
            'preparation_time_minutes.min' => 'Preparation time must be at least 5 minutes.',
            'preparation_time_minutes.max' => 'Preparation time cannot exceed 3 hours.',
            'operating_hours.*.day.in' => 'Day must be a valid day of the week.',
            'operating_hours.*.open_time.date_format' => 'Open time must be in HH:MM format.',
            'operating_hours.*.close_time.date_format' => 'Close time must be in HH:MM format.',
            'auto_acceptance_criteria.min_value.min' => 'Minimum value cannot be negative.',
            'auto_acceptance_criteria.max_distance.min' => 'Maximum distance must be at least 1 km.',
            'auto_acceptance_criteria.max_distance.max' => 'Maximum distance cannot exceed 50 km.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate operating hours
            if ($this->has('operating_hours') && is_array($this->input('operating_hours'))) {
                foreach ($this->input('operating_hours') as $index => $hours) {
                    if (isset($hours['open_time']) && isset($hours['close_time']) && ! ($hours['is_closed'] ?? false)) {
                        if ($hours['open_time'] >= $hours['close_time']) {
                            $validator->errors()->add(
                                "operating_hours.{$index}.close_time",
                                'Close time must be after open time.'
                            );
                        }
                    }
                }
            }
        });
    }
}
