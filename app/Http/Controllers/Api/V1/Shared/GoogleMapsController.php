<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\Integration\GoogleMapsService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Google Maps API Controller
 *
 * Provides endpoints for Google Maps Platform integration:
 * - Address geocoding and validation
 * - Distance and duration calculations
 * - Route optimization
 * - Location validation
 */
class GoogleMapsController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly GoogleMapsService $googleMapsService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Geocode an address to get coordinates.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function geocodeAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:500',
            'country' => 'nullable|string|size:2',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $result = $this->googleMapsService->geocodeAddress(
                $request->address,
                $request->country
            );

            if (! $result) {
                return $this->errorResponse(
                    'Address could not be geocoded',
                    404,
                    'GEOCODING_FAILED'
                );
            }

            $this->loggingService->logInfo('Address geocoded successfully', [
                'address' => $request->address,
                'result' => $result,
            ]);

            return $this->successResponse($result, 'Address geocoded successfully');

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'geocode_address',
                'address' => $request->address,
            ]);

            return $this->errorResponse(
                'Failed to geocode address',
                500,
                'GEOCODING_ERROR'
            );
        }
    }

    /**
     * Reverse geocode coordinates to get address.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function reverseGeocode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $result = $this->googleMapsService->reverseGeocode(
                $request->latitude,
                $request->longitude
            );

            if (! $result) {
                return $this->errorResponse(
                    'Coordinates could not be reverse geocoded',
                    404,
                    'REVERSE_GEOCODING_FAILED'
                );
            }

            return $this->successResponse($result, 'Coordinates reverse geocoded successfully');

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'reverse_geocode',
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
            ]);

            return $this->errorResponse(
                'Failed to reverse geocode coordinates',
                500,
                'REVERSE_GEOCODING_ERROR'
            );
        }
    }

    /**
     * Calculate distance and duration between two points.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function calculateDistance(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'origin_latitude' => 'required|numeric|between:-90,90',
            'origin_longitude' => 'required|numeric|between:-180,180',
            'destination_latitude' => 'required|numeric|between:-90,90',
            'destination_longitude' => 'required|numeric|between:-180,180',
            'mode' => 'nullable|string|in:driving,walking,bicycling,transit',
            'avoid_tolls' => 'nullable|boolean',
            'avoid_highways' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $result = $this->googleMapsService->calculateDistanceAndDuration(
                $request->origin_latitude,
                $request->origin_longitude,
                $request->destination_latitude,
                $request->destination_longitude,
                $request->mode ?? 'driving',
                $request->boolean('avoid_tolls', false),
                $request->boolean('avoid_highways', false)
            );

            if (! $result) {
                return $this->errorResponse(
                    'Distance calculation failed',
                    500,
                    'DISTANCE_CALCULATION_FAILED'
                );
            }

            return $this->successResponse($result, 'Distance calculated successfully');

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'calculate_distance',
                'origin' => [$request->origin_latitude, $request->origin_longitude],
                'destination' => [$request->destination_latitude, $request->destination_longitude],
            ]);

            return $this->errorResponse(
                'Failed to calculate distance',
                500,
                'DISTANCE_CALCULATION_ERROR'
            );
        }
    }

    /**
     * Get optimized route with waypoints.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function getOptimizedRoute(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'origin.lat' => 'required|numeric|between:-90,90',
            'origin.lng' => 'required|numeric|between:-180,180',
            'destination.lat' => 'required|numeric|between:-90,90',
            'destination.lng' => 'required|numeric|between:-180,180',
            'waypoints' => 'nullable|array',
            'waypoints.*.lat' => 'required_with:waypoints|numeric|between:-90,90',
            'waypoints.*.lng' => 'required_with:waypoints|numeric|between:-180,180',
            'optimize_waypoints' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $result = $this->googleMapsService->getOptimizedRoute(
                $request->origin,
                $request->destination,
                $request->waypoints ?? [],
                $request->boolean('optimize_waypoints', true)
            );

            if (! $result) {
                return $this->errorResponse(
                    'Route optimization failed',
                    500,
                    'ROUTE_OPTIMIZATION_FAILED'
                );
            }

            return $this->successResponse($result, 'Route optimized successfully');

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'get_optimized_route',
                'origin' => $request->origin,
                'destination' => $request->destination,
            ]);

            return $this->errorResponse(
                'Failed to optimize route',
                500,
                'ROUTE_OPTIMIZATION_ERROR'
            );
        }
    }

    /**
     * Validate an address.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function validateAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $result = $this->googleMapsService->validateAddress($request->address);

            return $this->successResponse($result, 'Address validation completed');

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'validate_address',
                'address' => $request->address,
            ]);

            return $this->errorResponse(
                'Failed to validate address',
                500,
                'ADDRESS_VALIDATION_ERROR'
            );
        }
    }

    /**
     * Calculate delivery ETA.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function calculateDeliveryETA(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'origin_latitude' => 'required|numeric|between:-90,90',
            'origin_longitude' => 'required|numeric|between:-180,180',
            'destination_latitude' => 'required|numeric|between:-90,90',
            'destination_longitude' => 'required|numeric|between:-180,180',
            'pickup_time_minutes' => 'nullable|integer|min:0|max:120',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $eta = $this->googleMapsService->calculateDeliveryETA(
                $request->origin_latitude,
                $request->origin_longitude,
                $request->destination_latitude,
                $request->destination_longitude,
                $request->pickup_time_minutes ?? 10
            );

            if (! $eta) {
                return $this->errorResponse(
                    'ETA calculation failed',
                    500,
                    'ETA_CALCULATION_FAILED'
                );
            }

            return $this->successResponse([
                'estimated_delivery_time' => $eta->toISOString(),
                'estimated_delivery_time_formatted' => $eta->format('Y-m-d H:i:s'),
                'minutes_from_now' => now()->diffInMinutes($eta),
            ], 'Delivery ETA calculated successfully');

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'calculate_delivery_eta',
                'origin' => [$request->origin_latitude, $request->origin_longitude],
                'destination' => [$request->destination_latitude, $request->destination_longitude],
            ]);

            return $this->errorResponse(
                'Failed to calculate delivery ETA',
                500,
                'ETA_CALCULATION_ERROR'
            );
        }
    }

    /**
     * Test Google Maps API connection.
     *
     * @group Google Maps
     *
     * @authenticated
     */
    public function testConnection(): JsonResponse
    {
        try {
            $result = $this->googleMapsService->testConnection();

            if ($result['success']) {
                return $this->successResponse($result, 'Google Maps API connection successful');
            } else {
                return $this->errorResponse(
                    $result['error'],
                    503,
                    'GOOGLE_MAPS_CONNECTION_FAILED'
                );
            }

        } catch (\Exception $e) {
            $this->loggingService->logException($e, [
                'action' => 'test_google_maps_connection',
            ]);

            return $this->errorResponse(
                'Failed to test Google Maps connection',
                500,
                'CONNECTION_TEST_ERROR'
            );
        }
    }
}
