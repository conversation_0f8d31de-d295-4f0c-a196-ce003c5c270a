<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\User\ChangePasswordRequest;
use App\Http\Requests\Api\V1\User\UpdatePreferencesRequest;
use App\Http\Requests\Api\V1\User\UpdateProfileRequest;
use App\Http\Resources\Api\V1\UserProfileResource;
use App\Services\System\LoggingService;
use App\Services\User\UserProfileService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Unified User Profile Management Controller
 *
 * Handles user profile operations for both central and tenant domain users:
 * - Central users: Customers, Platform admins (no tenant_id)
 * - Tenant users: Business owners/staff, Provider owners/staff (with tenant_id)
 *
 * This controller automatically detects context and provides appropriate
 * profile management for users accessing any domain.
 */
class UserProfileController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly UserProfileService $userProfileService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get current user's profile.
     */
    public function show(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $context = $this->getUserContext($user);

            // Validate user access based on context
            if (! $this->validateUserAccess($user, $context)) {
                return $this->forbiddenResponse($this->getAccessErrorMessage($context));
            }

            $user->load($this->getProfileRelations($context));

            return $this->successResponse(
                new UserProfileResource($user),
                $this->getSuccessMessage('retrieved', $context)
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, [
                'action' => 'get_user_profile',
                'user_id' => $request->user()->id,
                'tenant_id' => tenant()?->id,
                'context' => $this->getUserContext($request->user()),
            ]);

            return $this->serverErrorResponse('Failed to retrieve profile');
        }
    }

    /**
     * Update user's profile information.
     */
    public function update(UpdateProfileRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            $context = $this->getUserContext($user);

            // Validate user access based on context
            if (! $this->validateUserAccess($user, $context)) {
                return $this->forbiddenResponse($this->getAccessErrorMessage($context));
            }

            $user = $this->userProfileService->updateProfile(
                $user,
                $request->validated()
            );

            $this->loggingService->logAuth($this->getLogAction('profile_updated', $context), [
                'user_id' => $user->id,
                'tenant_id' => tenant()?->id,
                'updated_fields' => array_keys($request->validated()),
                'context' => $context,
            ], $user->id);

            return $this->successResponse(
                new UserProfileResource($user->load($this->getProfileRelations($context))),
                $this->getSuccessMessage('updated', $context)
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, [
                'action' => 'update_user_profile',
                'user_id' => $request->user()->id,
                'tenant_id' => tenant()?->id,
                'context' => $this->getUserContext($request->user()),
            ]);

            return $this->serverErrorResponse('Failed to update profile');
        }
    }

    /**
     * Change user's password.
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            $context = $this->getUserContext($user);

            // Validate user access based on context
            if (! $this->validateUserAccess($user, $context)) {
                return $this->forbiddenResponse($this->getAccessErrorMessage($context));
            }

            $success = $this->userProfileService->changePassword(
                $user,
                $request->validated('current_password'),
                $request->validated('new_password')
            );

            if ($success) {
                $this->loggingService->logAuth($this->getLogAction('password_changed', $context), [
                    'user_id' => $user->id,
                    'tenant_id' => tenant()?->id,
                    'context' => $context,
                ], $user->id);

                return $this->successResponse(
                    null,
                    'Password changed successfully'
                );
            }

            return $this->errorResponse('Current password is incorrect', 400);

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, [
                'action' => 'change_user_password',
                'user_id' => $request->user()->id,
                'tenant_id' => tenant()?->id,
                'context' => $this->getUserContext($request->user()),
            ]);

            return $this->serverErrorResponse('Failed to change password');
        }
    }

    /**
     * Get user's preferences.
     */
    public function getPreferences(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $context = $this->getUserContext($user);

            // Validate user access based on context
            if (! $this->validateUserAccess($user, $context)) {
                return $this->forbiddenResponse($this->getAccessErrorMessage($context));
            }

            $preferences = $this->userProfileService->getUserPreferences($user);

            return $this->successResponse(
                $preferences,
                $this->getSuccessMessage('preferences retrieved', $context)
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, [
                'action' => 'get_user_preferences',
                'user_id' => $request->user()->id,
                'tenant_id' => tenant()?->id,
                'context' => $this->getUserContext($request->user()),
            ]);

            return $this->serverErrorResponse('Failed to retrieve preferences');
        }
    }

    /**
     * Update user's preferences.
     */
    public function updatePreferences(UpdatePreferencesRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            $context = $this->getUserContext($user);

            // Validate user access based on context
            if (! $this->validateUserAccess($user, $context)) {
                return $this->forbiddenResponse($this->getAccessErrorMessage($context));
            }

            $preferences = $this->userProfileService->updateUserPreferences(
                $user,
                $request->validated()
            );

            $this->loggingService->logAuth($this->getLogAction('preferences_updated', $context), [
                'user_id' => $user->id,
                'tenant_id' => tenant()?->id,
                'updated_preferences' => array_keys($request->validated()),
                'context' => $context,
            ], $user->id);

            return $this->successResponse(
                $preferences,
                $this->getSuccessMessage('preferences updated', $context)
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, [
                'action' => 'update_user_preferences',
                'user_id' => $request->user()->id,
                'tenant_id' => tenant()?->id,
                'context' => $this->getUserContext($request->user()),
            ]);

            return $this->serverErrorResponse('Failed to update preferences');
        }
    }

    /**
     * Deactivate user's account.
     */
    public function deactivate(Request $request): JsonResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
            'password' => 'required|string',
        ]);

        try {
            $user = $request->user();
            $context = $this->getUserContext($user);

            // Validate user access based on context
            if (! $this->validateUserAccess($user, $context)) {
                return $this->forbiddenResponse($this->getAccessErrorMessage($context));
            }

            $success = $this->userProfileService->deactivateAccount(
                $user,
                $request->password,
                $request->reason
            );

            if ($success) {
                $this->loggingService->logAuth($this->getLogAction('account_deactivated', $context), [
                    'user_id' => $user->id,
                    'tenant_id' => tenant()?->id,
                    'reason' => $request->reason,
                    'context' => $context,
                ], $user->id);

                return $this->successResponse(
                    null,
                    $this->getSuccessMessage('account deactivated', $context)
                );
            }

            return $this->errorResponse('Failed to deactivate account', 400);

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, [
                'action' => 'deactivate_user_account',
                'user_id' => $request->user()->id,
                'tenant_id' => tenant()?->id,
                'context' => $this->getUserContext($request->user()),
            ]);

            return $this->serverErrorResponse('Failed to deactivate account');
        }
    }

    /**
     * Determine user context based on tenant_id and current domain.
     */
    private function getUserContext($user): string
    {
        // If user has no tenant_id, they're a central user
        if ($user->tenant_id === null) {
            return 'central';
        }

        // If user has tenant_id, they're a tenant user
        return 'tenant';
    }

    /**
     * Validate user access based on context.
     */
    private function validateUserAccess($user, string $context): bool
    {
        if ($context === 'central') {
            // Central users must have no tenant_id
            return $user->tenant_id === null;
        }

        if ($context === 'tenant') {
            // Tenant users must have tenant_id matching current tenant
            $tenant = tenant();

            return $user->tenant_id !== null && $user->tenant_id === $tenant?->id;
        }

        return false;
    }

    /**
     * Get appropriate profile relations based on context.
     */
    private function getProfileRelations(string $context): array
    {
        $baseRelations = [
            'roles',
            'abilities',
            'addresses',
            'userPreferences',
        ];

        if ($context === 'central') {
            // Central users may have customer profiles
            $baseRelations[] = 'customerProfile';
        } else {
            // Tenant users have tenant relationship
            $baseRelations[] = 'tenant';
        }

        return $baseRelations;
    }

    /**
     * Get access error message based on context.
     */
    private function getAccessErrorMessage(string $context): string
    {
        return match ($context) {
            'central' => 'This endpoint is for central users only',
            'tenant' => 'This endpoint is for tenant users only',
            default => 'Access denied',
        };
    }

    /**
     * Get success message based on action and context.
     */
    private function getSuccessMessage(string $action, string $context): string
    {
        $contextLabel = ucfirst($context);

        return "{$contextLabel} user {$action} successfully";
    }

    /**
     * Get log action name based on action and context.
     */
    private function getLogAction(string $action, string $context): string
    {
        return "{$context}_{$action}";
    }
}
