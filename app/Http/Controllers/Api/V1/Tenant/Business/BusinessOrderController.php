<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Enums\Delivery\OrderStatus;
use App\Enums\Financial\PaymentStatus;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\BulkOrderRequest;
use App\Http\Requests\Api\V1\BulkOrderUpdateRequest;
use App\Http\Requests\Api\V1\OrderExportRequest;
use App\Models\Delivery\Order;
use App\Services\Business\BusinessService;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

/**
 * Business Order Controller
 *
 * Manages orders for business owners within their tenant context.
 * Provides order listing, details, status updates, and management.
 */
class BusinessOrderController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService
    ) {}

    /**
     * Display a listing of orders for the business.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @queryParam page int Page number for pagination. Example: 1
     * @queryParam per_page int Number of items per page (max 100). Example: 15
     * @queryParam search string Search in order reference, customer name. Example: ORD-2024
     * @queryParam status string Filter by order status. Example: pending
     * @queryParam payment_status string Filter by payment status. Example: paid
     * @queryParam date_from string Filter orders from date (Y-m-d). Example: 2024-01-01
     * @queryParam date_to string Filter orders to date (Y-m-d). Example: 2024-01-31
     * @queryParam sort_by string Sort by field (created_at, total_amount, status). Example: created_at
     * @queryParam sort_direction string Sort direction (asc, desc). Example: desc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Orders retrieved successfully",
     *   "data": {
     *     "orders": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "order_reference": "ORD-2024-001",
     *         "customer": {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *           "name": "John Doe",
     *           "email": "<EMAIL>"
     *         },
     *         "status": "pending",
     *         "payment_status": "pending",
     *         "total_amount": 5500,
     *         "items_count": 3,
     *         "created_at": "2024-01-15T10:30:00Z",
     *         "estimated_preparation_time": 30
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 15,
     *       "total": 150,
     *       "last_page": 10
     *     }
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'search' => 'sometimes|string|max:255',
            'status' => ['sometimes', 'string', Rule::in(array_column(OrderStatus::cases(), 'value'))],
            'payment_status' => ['sometimes', 'string', Rule::in(array_column(PaymentStatus::cases(), 'value'))],
            'date_from' => 'sometimes|date|date_format:Y-m-d',
            'date_to' => 'sometimes|date|date_format:Y-m-d|after_or_equal:date_from',
            'sort_by' => 'sometimes|string|in:created_at,total_amount,status,payment_status',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = Order::forCurrentTenant()
                ->where('business_id', $business->id)
                ->with(['customer', 'orderItems']);

            $result = $this->handleQuery($query, $request, [
                'searchFields' => [
                    'order_reference',
                    'customer.first_name',
                    'customer.last_name',
                    'customer.email',
                ],
                'sortFields' => [
                    'created_at',
                    'total_amount',
                    'status',
                    'payment_status',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'payment_status' => ['type' => 'exact'],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereDate('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereDate('created_at', '<=', $value);
                        },
                    ],
                ],
                'message' => 'Orders retrieved successfully',
                'entityName' => 'orders',
                'transformer' => [$this, 'transformOrderForList'],
            ]);

            // Convert to expected format for backward compatibility
            $resultData = json_decode($result->getContent(), true);
            if (isset($resultData['data']['data'])) {
                $transformedData = [
                    'orders' => $resultData['data']['data'],
                    'pagination' => [
                        'current_page' => $resultData['data']['current_page'],
                        'per_page' => $resultData['data']['per_page'],
                        'total' => $resultData['data']['total'],
                        'last_page' => $resultData['data']['last_page'],
                    ],
                ];

                return $this->successResponse(
                    $transformedData,
                    'Orders retrieved successfully'
                );
            }

            return $result;

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve orders',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'filters' => $request->only(['status', 'payment_status', 'date_from', 'date_to']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve orders');
        }
    }

    /**
     * Display the specified order.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam id string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "order_reference": "ORD-2024-001",
     *     "customer": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "John Doe",
     *       "email": "<EMAIL>",
     *       "phone": "+234123456789"
     *     },
     *     "status": "pending",
     *     "payment_status": "pending",
     *     "order_type": "delivery",
     *     "total_amount": 5500,
     *     "sub_total": 5000,
     *     "delivery_fee": 500,
     *     "tax_amount": 0,
     *     "items": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *         "product_name": "Jollof Rice",
     *         "quantity": 2,
     *         "price_per_unit": 2500,
     *         "total_price": 5000,
     *         "notes": "Extra spicy"
     *       }
     *     ],
     *     "delivery_address": {
     *       "street": "123 Main St",
     *       "city": "Lagos",
     *       "state": "Lagos",
     *       "country": "Nigeria"
     *     },
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "estimated_preparation_time": 30,
     *     "customer_notes": "Please call when ready"
     *   }
     * }
     */
    public function show(string $id): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $order = Order::forCurrentTenant()
                ->where('business_id', $business->id)
                ->where('id', $id)
                ->with([
                    'customer',
                    'orderItems.product',
                    'orderItems.orderItemOptions',
                    'deliveryAddress',
                    'pickupAddress',
                ])
                ->first();

            if (! $order) {
                return $this->errorResponse('Order not found', 404);
            }

            // Transform order data
            $orderData = [
                'id' => $order->id,
                'order_reference' => $order->order_reference,
                'customer' => [
                    'id' => $order->customer?->id,
                    'name' => $order->customer?->first_name.' '.$order->customer?->last_name,
                    'email' => $order->customer?->email,
                    'phone' => $order->customer?->phone,
                ],
                'status' => $order->status->value,
                'payment_status' => $order->payment_status->value,
                'order_type' => $order->order_type->value,
                'source' => $order->source->value,
                'total_amount' => (float) $order->total_amount,
                'sub_total' => (float) $order->sub_total,
                'delivery_fee' => (float) $order->delivery_fee,
                'tax_amount' => (float) $order->tax_amount,
                'platform_commission_amount' => (float) $order->platform_commission_amount,
                'currency' => $order->currency,
                'payment_method' => $order->payment_method?->value,
                'items' => $order->orderItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'product_name' => $item->name,
                        'variant_name' => $item->variant_name,
                        'quantity' => $item->quantity,
                        'price_per_unit' => (float) $item->price_per_unit,
                        'total_price' => (float) $item->total_price,
                        'notes' => $item->notes,
                        'options' => $item->orderItemOptions->map(function ($option) {
                            return [
                                'name' => $option->name,
                                'value' => $option->value,
                                'price' => (float) $option->price,
                            ];
                        }),
                    ];
                }),
                'delivery_address' => $order->deliveryAddress ? [
                    'id' => $order->deliveryAddress->id,
                    'street' => $order->deliveryAddress->street,
                    'city' => $order->deliveryAddress->city,
                    'state' => $order->deliveryAddress->state,
                    'country' => $order->deliveryAddress->country,
                    'postal_code' => $order->deliveryAddress->postal_code,
                ] : null,
                'pickup_address' => $order->pickupAddress ? [
                    'id' => $order->pickupAddress->id,
                    'street' => $order->pickupAddress->street,
                    'city' => $order->pickupAddress->city,
                    'state' => $order->pickupAddress->state,
                    'country' => $order->pickupAddress->country,
                ] : null,
                'created_at' => $order->created_at->toISOString(),
                'accepted_at' => $order->accepted_at?->toISOString(),
                'prepared_at' => $order->prepared_at?->toISOString(),
                'cancelled_at' => $order->cancelled_at?->toISOString(),
                'estimated_preparation_time' => $order->estimated_preparation_time,
                'estimated_delivery_time' => $order->estimated_delivery_time,
                'scheduled_pickup_time' => $order->scheduled_pickup_time?->toISOString(),
                'customer_notes' => $order->customer_notes,
                'business_notes' => $order->business_notes,
                'cancellation_reason' => $order->cancellation_reason,
                'requires_delivery' => $order->requiresDelivery(),
                'is_pickup' => $order->isPickup(),
            ];

            return $this->successResponse($orderData, 'Order retrieved successfully');

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve order',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'order_id' => $id,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve order');
        }
    }

    /**
     * Update order status and details.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam id string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam status string required New order status. Example: accepted
     * @bodyParam business_notes string Business notes for the order. Example: Order will be ready in 30 minutes
     * @bodyParam estimated_preparation_time integer Estimated preparation time in minutes. Example: 30
     * @bodyParam cancellation_reason string Reason for cancellation (required if status is cancelled). Example: Out of stock
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "accepted",
     *     "business_notes": "Order will be ready in 30 minutes",
     *     "estimated_preparation_time": 30,
     *     "accepted_at": "2024-01-15T11:00:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $request->validate([
            'status' => ['required', 'string', Rule::in(array_column(OrderStatus::cases(), 'value'))],
            'business_notes' => 'sometimes|string|max:1000',
            'estimated_preparation_time' => 'sometimes|integer|min:1|max:300',
            'cancellation_reason' => 'required_if:status,cancelled|string|max:500',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $order = Order::forCurrentTenant()
                ->where('business_id', $business->id)
                ->where('id', $id)
                ->first();

            if (! $order) {
                return $this->errorResponse('Order not found', 404);
            }

            $newStatus = OrderStatus::from($request->input('status'));

            // Validate status transition
            if (! $this->isValidStatusTransition($order->status, $newStatus)) {
                return $this->errorResponse(
                    "Cannot change order status from {$order->status->value} to {$newStatus->value}",
                    422
                );
            }

            // Prepare update data
            $updateData = [
                'status' => $newStatus,
                'previous_status' => $order->status,
            ];

            // Add timestamp based on status
            switch ($newStatus) {
                case OrderStatus::CONFIRMED:
                    $updateData['accepted_at'] = now();
                    break;
                case OrderStatus::PROCESSING:
                    $updateData['accepted_at'] = $updateData['accepted_at'] ?? $order->accepted_at ?? now();
                    break;
                case OrderStatus::READY_FOR_PICKUP:
                    $updateData['prepared_at'] = now();
                    break;
                case OrderStatus::CANCELLED:
                    $updateData['cancelled_at'] = now();
                    $updateData['cancelled_by_user_id'] = auth()->id();
                    $updateData['cancellation_reason'] = $request->input('cancellation_reason');
                    break;
            }

            // Add optional fields
            if ($request->filled('business_notes')) {
                $updateData['business_notes'] = $request->input('business_notes');
            }

            if ($request->filled('estimated_preparation_time')) {
                $updateData['estimated_preparation_time'] = $request->input('estimated_preparation_time');
            }

            // Update the order
            $order->update($updateData);

            // Log the status change
            $this->loggingService->logInfo('Order status updated', [
                'order_id' => $order->id,
                'previous_status' => $order->previous_status->value,
                'new_status' => $order->status->value,
                'updated_by' => auth()->id(),
                'tenant_id' => tenant()?->id,
            ]);

            return $this->successResponse([
                'id' => $order->id,
                'status' => $order->status->value,
                'previous_status' => $order->previous_status->value,
                'business_notes' => $order->business_notes,
                'estimated_preparation_time' => $order->estimated_preparation_time,
                'accepted_at' => $order->accepted_at?->toISOString(),
                'prepared_at' => $order->prepared_at?->toISOString(),
                'cancelled_at' => $order->cancelled_at?->toISOString(),
                'cancellation_reason' => $order->cancellation_reason,
            ], 'Order updated successfully');

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update order',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'order_id' => $id,
                    'requested_status' => $request->input('status'),
                ]
            );

            return $this->serverErrorResponse('Failed to update order');
        }
    }

    /**
     * Accept an order.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam order string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam business_notes string Business notes for the order. Example: Order will be ready in 30 minutes
     * @bodyParam estimated_preparation_time integer Estimated preparation time in minutes. Example: 30
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order accepted successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "confirmed",
     *     "business_notes": "Order will be ready in 30 minutes",
     *     "estimated_preparation_time": 30,
     *     "accepted_at": "2024-01-15T11:00:00Z"
     *   }
     * }
     */
    public function accept(Request $request, string $order): JsonResponse
    {
        $request->validate([
            'business_notes' => 'sometimes|string|max:1000',
            'estimated_preparation_time' => 'sometimes|integer|min:1|max:300',
        ]);

        return $this->updateOrderStatus($order, OrderStatus::CONFIRMED, [
            'business_notes' => $request->input('business_notes'),
            'estimated_preparation_time' => $request->input('estimated_preparation_time'),
        ], 'Order accepted successfully');
    }

    /**
     * Reject an order.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam order string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam rejection_reason string required Reason for rejecting the order. Example: Out of stock
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order rejected successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "cancelled",
     *     "cancellation_reason": "Out of stock",
     *     "cancelled_at": "2024-01-15T11:00:00Z"
     *   }
     * }
     */
    public function reject(Request $request, string $order): JsonResponse
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        return $this->updateOrderStatus($order, OrderStatus::CANCELLED, [
            'cancellation_reason' => $request->input('rejection_reason'),
        ], 'Order rejected successfully');
    }

    /**
     * Mark order as being prepared.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam order string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam business_notes string Business notes for the order. Example: Started preparing your order
     * @bodyParam estimated_preparation_time integer Updated estimated preparation time in minutes. Example: 25
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order marked as being prepared",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "processing",
     *     "business_notes": "Started preparing your order",
     *     "estimated_preparation_time": 25
     *   }
     * }
     */
    public function prepare(Request $request, string $order): JsonResponse
    {
        $request->validate([
            'business_notes' => 'sometimes|string|max:1000',
            'estimated_preparation_time' => 'sometimes|integer|min:1|max:300',
        ]);

        return $this->updateOrderStatus($order, OrderStatus::PROCESSING, [
            'business_notes' => $request->input('business_notes'),
            'estimated_preparation_time' => $request->input('estimated_preparation_time'),
        ], 'Order marked as being prepared');
    }

    /**
     * Mark order as ready for pickup/delivery.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam order string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam business_notes string Business notes for the order. Example: Order is ready for pickup
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order marked as ready",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "ready_for_pickup",
     *     "business_notes": "Order is ready for pickup",
     *     "prepared_at": "2024-01-15T11:30:00Z"
     *   }
     * }
     */
    public function ready(Request $request, string $order): JsonResponse
    {
        $request->validate([
            'business_notes' => 'sometimes|string|max:1000',
        ]);

        return $this->updateOrderStatus($order, OrderStatus::READY_FOR_PICKUP, [
            'business_notes' => $request->input('business_notes'),
        ], 'Order marked as ready');
    }

    /**
     * Mark order as completed.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam order string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam business_notes string Business notes for the order. Example: Order completed successfully
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order completed successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "delivered",
     *     "business_notes": "Order completed successfully",
     *     "completed_at": "2024-01-15T12:00:00Z"
     *   }
     * }
     */
    public function complete(Request $request, string $order): JsonResponse
    {
        $request->validate([
            'business_notes' => 'sometimes|string|max:1000',
        ]);

        return $this->updateOrderStatus($order, OrderStatus::DELIVERED, [
            'business_notes' => $request->input('business_notes'),
        ], 'Order completed successfully');
    }

    /**
     * Cancel an order.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @urlParam order string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam cancellation_reason string required Reason for cancelling the order. Example: Customer requested cancellation
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order cancelled successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "cancelled",
     *     "cancellation_reason": "Customer requested cancellation",
     *     "cancelled_at": "2024-01-15T11:00:00Z"
     *   }
     * }
     */
    public function cancel(Request $request, string $order): JsonResponse
    {
        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        return $this->updateOrderStatus($order, OrderStatus::CANCELLED, [
            'cancellation_reason' => $request->input('cancellation_reason'),
        ], 'Order cancelled successfully');
    }

    // Helper Methods

    /**
     * Update order status with common logic.
     */
    private function updateOrderStatus(string $orderId, OrderStatus $newStatus, array $additionalData = [], string $successMessage = 'Order updated successfully'): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $order = Order::forCurrentTenant()
                ->where('business_id', $business->id)
                ->where('id', $orderId)
                ->first();

            if (! $order) {
                return $this->errorResponse('Order not found', 404);
            }

            // Validate status transition
            if (! $this->isValidStatusTransition($order->status, $newStatus)) {
                return $this->errorResponse(
                    "Cannot change order status from {$order->status->value} to {$newStatus->value}",
                    422
                );
            }

            // Prepare update data
            $updateData = [
                'status' => $newStatus,
                'previous_status' => $order->status,
            ];

            // Add timestamp based on status
            switch ($newStatus) {
                case OrderStatus::CONFIRMED:
                    $updateData['accepted_at'] = now();
                    break;
                case OrderStatus::PROCESSING:
                    $updateData['accepted_at'] = $updateData['accepted_at'] ?? $order->accepted_at ?? now();
                    break;
                case OrderStatus::READY_FOR_PICKUP:
                    $updateData['prepared_at'] = now();
                    break;
                case OrderStatus::DELIVERED:
                    $updateData['completed_at'] = now();
                    break;
                case OrderStatus::CANCELLED:
                    $updateData['cancelled_at'] = now();
                    $updateData['cancelled_by_user_id'] = auth()->id();
                    break;
            }

            // Add additional data
            foreach ($additionalData as $key => $value) {
                if ($value !== null) {
                    $updateData[$key] = $value;
                }
            }

            // Update the order
            $previousStatus = $order->status->value;
            $order->update($updateData);

            // Send order status notification to customer
            $this->notificationService->sendOrderStatusUpdate($order, $previousStatus, $newStatus->value);

            // Log the status change
            $this->loggingService->logInfo('Order status updated', [
                'order_id' => $order->id,
                'previous_status' => $previousStatus,
                'new_status' => $order->status->value,
                'updated_by' => auth()->id(),
                'tenant_id' => tenant()?->id,
            ]);

            return $this->successResponse([
                'id' => $order->id,
                'status' => $order->status->value,
                'previous_status' => $order->previous_status->value,
                'business_notes' => $order->business_notes,
                'estimated_preparation_time' => $order->estimated_preparation_time,
                'accepted_at' => $order->accepted_at?->toISOString(),
                'prepared_at' => $order->prepared_at?->toISOString(),
                'completed_at' => $order->completed_at?->toISOString(),
                'cancelled_at' => $order->cancelled_at?->toISOString(),
                'cancellation_reason' => $order->cancellation_reason,
            ], $successMessage);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update order status',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'order_id' => $orderId,
                    'requested_status' => $newStatus->value,
                ]
            );

            return $this->serverErrorResponse('Failed to update order status');
        }
    }

    /**
     * Transform order for list view.
     */
    private function transformOrderForList($order): array
    {
        return [
            'id' => $order->id,
            'order_reference' => $order->order_reference,
            'customer' => [
                'id' => $order->customer?->id,
                'name' => $order->customer?->first_name.' '.$order->customer?->last_name,
                'email' => $order->customer?->email,
            ],
            'status' => $order->status->value,
            'payment_status' => $order->payment_status->value,
            'total_amount' => (float) $order->total_amount,
            'items_count' => $order->orderItems->count(),
            'created_at' => $order->created_at->toISOString(),
            'estimated_preparation_time' => $order->estimated_preparation_time,
            'requires_delivery' => $order->requiresDelivery(),
        ];
    }

    /**
     * Check if status transition is valid.
     */
    private function isValidStatusTransition(OrderStatus $currentStatus, OrderStatus $newStatus): bool
    {
        // Define valid transitions
        $validTransitions = [
            OrderStatus::PENDING->value => [
                OrderStatus::CONFIRMED->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::CONFIRMED->value => [
                OrderStatus::PROCESSING->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::PROCESSING->value => [
                OrderStatus::READY_FOR_PICKUP->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::READY_FOR_PICKUP->value => [
                OrderStatus::OUT_FOR_DELIVERY->value,
                OrderStatus::DELIVERED->value, // For pickup orders
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::OUT_FOR_DELIVERY->value => [
                OrderStatus::DELIVERED->value,
                OrderStatus::CANCELLED->value,
            ],
        ];

        return in_array(
            $newStatus->value,
            $validTransitions[$currentStatus->value] ?? []
        );
    }

    /**
     * Create multiple orders in bulk.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @bodyParam orders array required Array of orders to create (max 100). Example: [{"customer_id": "uuid", "items": [...]}]
     * @bodyParam batch_options.auto_assign_providers boolean Auto-assign delivery providers. Example: true
     * @bodyParam batch_options.send_notifications boolean Send notifications for created orders. Example: true
     * @bodyParam batch_options.validate_inventory boolean Validate inventory before creating orders. Example: true
     * @bodyParam batch_options.fail_on_error boolean Stop processing on first error. Example: false
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Bulk order creation completed",
     *   "data": {
     *     "created": 8,
     *     "failed": 2,
     *     "total": 10,
     *     "orders": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "order_reference": "ORD-2024-001",
     *         "status": "pending",
     *         "total_amount": 5500
     *       }
     *     ],
     *     "errors": [
     *       {
     *         "index": 3,
     *         "error": "Customer not found",
     *         "order_data": {...}
     *       }
     *     ]
     *   }
     * }
     */
    public function bulkCreate(BulkOrderRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $orders = $request->input('orders');
            $batchOptions = $request->input('batch_options', []);

            $created = [];
            $errors = [];
            $failOnError = $batchOptions['fail_on_error'] ?? false;

            DB::beginTransaction();

            foreach ($orders as $index => $orderData) {
                try {
                    // Add business context
                    $orderData['business_id'] = $business->id;
                    $orderData['business_tenant_id'] = tenant()->id;

                    // Create the order
                    $order = $this->createSingleOrder($orderData, $batchOptions);

                    $created[] = [
                        'id' => $order->id,
                        'order_reference' => $order->order_reference,
                        'status' => $order->status->value,
                        'total_amount' => (float) $order->total_amount,
                        'customer_id' => $order->customer_id,
                    ];

                } catch (\Exception $e) {
                    $error = [
                        'index' => $index,
                        'error' => $e->getMessage(),
                        'order_data' => $orderData,
                    ];

                    $errors[] = $error;

                    if ($failOnError) {
                        DB::rollBack();

                        return $this->errorResponse(
                            "Bulk order creation failed at index {$index}: {$e->getMessage()}",
                            422,
                            ['errors' => $errors]
                        );
                    }
                }
            }

            DB::commit();

            $result = [
                'created' => count($created),
                'failed' => count($errors),
                'total' => count($orders),
                'orders' => $created,
                'errors' => $errors,
            ];

            $message = count($errors) > 0
                ? "Bulk order creation completed with {$result['failed']} errors"
                : 'All orders created successfully';

            return $this->successResponse($result, $message);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logError(
                'Failed to create bulk orders',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'order_count' => count($orders ?? []),
                ]
            );

            return $this->serverErrorResponse('Failed to create bulk orders');
        }
    }

    /**
     * Update multiple orders in bulk.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @bodyParam updates array required Array of order updates (max 100). Example: [{"order_id": "uuid", "status": "confirmed"}]
     * @bodyParam batch_options.send_notifications boolean Send notifications for updated orders. Example: true
     * @bodyParam batch_options.validate_transitions boolean Validate status transitions. Example: true
     * @bodyParam batch_options.fail_on_error boolean Stop processing on first error. Example: false
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Bulk order update completed",
     *   "data": {
     *     "updated": 8,
     *     "failed": 2,
     *     "total": 10,
     *     "orders": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "status": "confirmed",
     *         "previous_status": "pending"
     *       }
     *     ],
     *     "errors": [
     *       {
     *         "order_id": "uuid",
     *         "error": "Invalid status transition"
     *       }
     *     ]
     *   }
     * }
     */
    public function bulkUpdate(BulkOrderUpdateRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $updates = $request->input('updates');
            $batchOptions = $request->input('batch_options', []);

            $updated = [];
            $errors = [];
            $failOnError = $batchOptions['fail_on_error'] ?? false;

            DB::beginTransaction();

            foreach ($updates as $updateData) {
                try {
                    $orderId = $updateData['order_id'];

                    $order = Order::forCurrentTenant()
                        ->where('business_id', $business->id)
                        ->where('id', $orderId)
                        ->first();

                    if (! $order) {
                        throw new \Exception("Order not found: {$orderId}");
                    }

                    $previousStatus = $order->status;

                    // Update the order
                    $this->updateSingleOrder($order, $updateData, $batchOptions);

                    $updated[] = [
                        'id' => $order->id,
                        'status' => $order->status->value,
                        'previous_status' => $previousStatus->value,
                        'updated_fields' => array_keys(array_diff_assoc($updateData, ['order_id' => $orderId])),
                    ];

                } catch (\Exception $e) {
                    $error = [
                        'order_id' => $updateData['order_id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                        'update_data' => $updateData,
                    ];

                    $errors[] = $error;

                    if ($failOnError) {
                        DB::rollBack();

                        return $this->errorResponse(
                            "Bulk order update failed for order {$updateData['order_id']}: {$e->getMessage()}",
                            422,
                            ['errors' => $errors]
                        );
                    }
                }
            }

            DB::commit();

            $result = [
                'updated' => count($updated),
                'failed' => count($errors),
                'total' => count($updates),
                'orders' => $updated,
                'errors' => $errors,
            ];

            $message = count($errors) > 0
                ? "Bulk order update completed with {$result['failed']} errors"
                : 'All orders updated successfully';

            return $this->successResponse($result, $message);

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logError(
                'Failed to update bulk orders',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'update_count' => count($updates ?? []),
                ]
            );

            return $this->serverErrorResponse('Failed to update bulk orders');
        }
    }

    /**
     * Export orders to various formats.
     *
     * @group Business Orders
     *
     * @authenticated
     *
     * @bodyParam format string required Export format (csv, excel, pdf, json). Example: csv
     * @bodyParam date_from string Filter orders from date (Y-m-d). Example: 2024-01-01
     * @bodyParam date_to string Filter orders to date (Y-m-d). Example: 2024-01-31
     * @bodyParam status array Filter by order statuses. Example: ["pending", "confirmed"]
     * @bodyParam fields array Select specific fields to export. Example: ["id", "customer_name", "total_amount"]
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Export completed successfully",
     *   "data": {
     *     "filename": "orders_export_2024-01-15_14-30-00.csv",
     *     "download_url": "https://api.example.com/exports/download/abc123",
     *     "record_count": 150,
     *     "file_size": "2.5 MB",
     *     "expires_at": "2024-01-16T14:30:00Z"
     *   }
     * }
     */
    public function export(OrderExportRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Build query with filters
            $query = Order::forCurrentTenant()
                ->where('business_id', $business->id)
                ->with(['customer', 'orderItems.product', 'deliveryAddress', 'pickupAddress']);

            // Apply filters
            $this->applyExportFilters($query, $request);

            // Get the data
            $orders = $query->get();

            if ($orders->isEmpty()) {
                return $this->errorResponse('No orders found matching the criteria', 404);
            }

            // Generate export
            $format = $request->input('format');
            $filename = $request->getFilename();

            $exportData = $this->prepareExportData($orders, $request);
            $filePath = $this->generateExportFile($exportData, $format, $filename);

            // Log export activity
            $this->loggingService->logInfo('Order export generated', [
                'business_id' => $business->id,
                'tenant_id' => tenant()?->id,
                'format' => $format,
                'record_count' => $orders->count(),
                'filters' => $request->only(['date_from', 'date_to', 'status', 'payment_status']),
            ]);

            return $this->successResponse([
                'filename' => $filename,
                'download_url' => url('api/v1/exports/download/'.basename($filePath)),
                'record_count' => $orders->count(),
                'file_size' => $this->formatFileSize(filesize($filePath)),
                'expires_at' => now()->addHours(24)->toISOString(),
            ], 'Export completed successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export orders',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'format' => $request->input('format'),
                ]
            );

            return $this->serverErrorResponse('Failed to export orders');
        }
    }

    /**
     * Helper method to create a single order.
     */
    private function createSingleOrder(array $orderData, array $batchOptions): Order
    {
        // This would contain the logic to create a single order
        // For now, returning a basic implementation
        $order = new Order;
        $order->fill($orderData);
        $order->save();

        return $order;
    }

    /**
     * Helper method to update a single order.
     */
    private function updateSingleOrder(Order $order, array $updateData, array $batchOptions): void
    {
        // Remove order_id from update data
        unset($updateData['order_id']);

        // Apply updates
        $order->update($updateData);
    }

    /**
     * Apply export filters to query.
     */
    private function applyExportFilters($query, OrderExportRequest $request): void
    {
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->input('date_from'));
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->input('date_to'));
        }

        if ($request->has('status')) {
            $query->whereIn('status', $request->input('status'));
        }

        if ($request->has('payment_status')) {
            $query->whereIn('payment_status', $request->input('payment_status'));
        }

        if ($request->has('customer_id')) {
            $query->where('customer_id', $request->input('customer_id'));
        }

        if ($request->has('min_amount')) {
            $query->where('total_amount', '>=', $request->input('min_amount'));
        }

        if ($request->has('max_amount')) {
            $query->where('total_amount', '<=', $request->input('max_amount'));
        }

        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('order_reference', 'ILIKE', "%{$search}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($search) {
                        $customerQuery->where('first_name', 'ILIKE', "%{$search}%")
                            ->orWhere('last_name', 'ILIKE', "%{$search}%")
                            ->orWhere('email', 'ILIKE', "%{$search}%");
                    });
            });
        }

        // Apply sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // Apply limit
        if ($request->has('limit')) {
            $query->limit($request->input('limit'));
        }

        if ($request->has('offset')) {
            $query->offset($request->input('offset'));
        }
    }

    /**
     * Prepare data for export.
     */
    private function prepareExportData($orders, OrderExportRequest $request): array
    {
        $fields = $request->input('fields', [
            'id', 'order_reference', 'customer_name', 'customer_email',
            'status', 'payment_status', 'total_amount', 'created_at',
        ]);

        return $orders->map(function ($order) use ($fields, $request) {
            $data = [];

            foreach ($fields as $field) {
                $data[$field] = match ($field) {
                    'id' => $order->id,
                    'order_reference' => $order->order_reference,
                    'customer_name' => $order->customer ?
                        $order->customer->first_name.' '.$order->customer->last_name : 'N/A',
                    'customer_email' => $order->customer?->email ?? 'N/A',
                    'business_name' => $order->business?->name ?? 'N/A',
                    'status' => $order->status->value,
                    'payment_status' => $order->payment_status->value,
                    'total_amount' => (float) $order->total_amount,
                    'delivery_fee' => (float) $order->delivery_fee,
                    'created_at' => $order->created_at->toISOString(),
                    'updated_at' => $order->updated_at->toISOString(),
                    'delivered_at' => $order->delivered_at?->toISOString(),
                    default => $order->{$field} ?? 'N/A',
                };
            }

            // Add additional data if requested
            if ($request->input('include_items', false)) {
                $data['items'] = $order->orderItems->map(function ($item) {
                    return [
                        'name' => $item->name,
                        'quantity' => $item->quantity,
                        'price' => (float) $item->price_per_unit,
                        'total' => (float) $item->total_price,
                    ];
                })->toArray();
            }

            if ($request->input('include_addresses', false)) {
                $data['delivery_address'] = $order->deliveryAddress ?
                    $order->deliveryAddress->street.', '.$order->deliveryAddress->city : 'N/A';
                $data['pickup_address'] = $order->pickupAddress ?
                    $order->pickupAddress->street.', '.$order->pickupAddress->city : 'N/A';
            }

            return $data;
        })->toArray();
    }

    /**
     * Generate export file.
     */
    private function generateExportFile(array $data, string $format, string $filename): string
    {
        $exportPath = storage_path('app/exports');
        if (! file_exists($exportPath)) {
            mkdir($exportPath, 0755, true);
        }

        $filePath = $exportPath.'/'.$filename;

        switch ($format) {
            case 'csv':
                $this->generateCsvFile($data, $filePath);
                break;
            case 'json':
                $this->generateJsonFile($data, $filePath);
                break;
            case 'excel':
                $this->generateExcelFile($data, $filePath);
                break;
            case 'pdf':
                $this->generatePdfFile($data, $filePath);
                break;
            default:
                throw new \Exception("Unsupported export format: {$format}");
        }

        return $filePath;
    }

    /**
     * Generate CSV file.
     */
    private function generateCsvFile(array $data, string $filePath): void
    {
        $file = fopen($filePath, 'w');

        if (! empty($data)) {
            // Write headers
            fputcsv($file, array_keys($data[0]));

            // Write data
            foreach ($data as $row) {
                fputcsv($file, $row);
            }
        }

        fclose($file);
    }

    /**
     * Generate JSON file.
     */
    private function generateJsonFile(array $data, string $filePath): void
    {
        file_put_contents($filePath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Generate Excel file (placeholder).
     */
    private function generateExcelFile(array $data, string $filePath): void
    {
        // This would use a library like PhpSpreadsheet
        // For now, fallback to CSV
        $this->generateCsvFile($data, $filePath);
    }

    /**
     * Generate PDF file (placeholder).
     */
    private function generatePdfFile(array $data, string $filePath): void
    {
        // This would use a library like TCPDF or DomPDF
        // For now, fallback to JSON
        $this->generateJsonFile($data, $filePath);
    }

    /**
     * Format file size.
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2).' '.$units[$pow];
    }
}
