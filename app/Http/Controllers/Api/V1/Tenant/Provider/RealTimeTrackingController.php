<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Driver;

use App\Http\Controllers\Controller;
use App\Models\Delivery\Delivery;
use App\Services\Delivery\RealTimeTrackingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Real-Time Tracking (Driver)
 *
 * APIs for drivers to update their location and manage real-time delivery tracking
 */
class RealTimeTrackingController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly RealTimeTrackingService $trackingService
    ) {}

    /**
     * Update Driver Location
     *
     * Update the driver's current location for real-time delivery tracking.
     *
     * @authenticated
     *
     * @bodyParam delivery_id string required The delivery ID being tracked. Example: uuid
     * @bodyParam latitude number required Current latitude. Example: 6.5244
     * @bodyParam longitude number required Current longitude. Example: 3.3792
     * @bodyParam accuracy number optional Location accuracy in meters. Example: 5.0
     * @bodyParam speed number optional Current speed in km/h. Example: 25.5
     * @bodyParam heading number optional Direction heading in degrees (0-360). Example: 180.0
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Location updated successfully",
     *   "data": {
     *     "delivery_id": "uuid",
     *     "location": {
     *       "latitude": 6.5244,
     *       "longitude": 3.3792,
     *       "accuracy": 5.0,
     *       "speed": 25.5,
     *       "heading": 180.0,
     *       "timestamp": "2024-01-15T10:30:00Z"
     *     },
     *     "estimated_arrival": "2024-01-15T11:15:00Z"
     *   }
     * }
     */
    public function updateLocation(Request $request): JsonResponse
    {
        $request->validate([
            'delivery_id' => 'required|uuid|exists:deliveries,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'accuracy' => 'sometimes|numeric|min:0',
            'speed' => 'sometimes|numeric|min:0',
            'heading' => 'sometimes|numeric|between:0,360',
        ]);

        $delivery = Delivery::findOrFail($request->input('delivery_id'));

        // Verify driver is assigned to this delivery
        if ($delivery->assigned_driver_id !== auth()->id()) {
            return $this->errorResponse('You are not assigned to this delivery', 403);
        }

        // Verify delivery is in trackable status
        if (! in_array($delivery->status->value, ['accepted', 'picked_up', 'en_route'])) {
            return $this->errorResponse('Delivery is not in a trackable status', 400);
        }

        $success = $this->trackingService->updateDeliveryLocation(
            $delivery,
            $request->input('latitude'),
            $request->input('longitude'),
            $request->input('accuracy'),
            $request->input('speed'),
            $request->input('heading'),
            auth()->user()
        );

        if (! $success) {
            return $this->errorResponse('Failed to update location', 500);
        }

        // Get updated delivery with estimated time
        $delivery->refresh();
        $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery->id);

        return $this->successResponse([
            'delivery_id' => $delivery->id,
            'location' => $currentLocation,
            'estimated_arrival' => $delivery->estimated_delivery_time?->toISOString(),
        ], 'Location updated successfully');
    }

    /**
     * Start Delivery Tracking
     *
     * Start real-time tracking for a delivery.
     *
     * @authenticated
     *
     * @bodyParam delivery_id string required The delivery ID to start tracking. Example: uuid
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery tracking started successfully",
     *   "data": {
     *     "delivery_id": "uuid",
     *     "tracking_started": true,
     *     "started_at": "2024-01-15T10:00:00Z"
     *   }
     * }
     */
    public function startTracking(Request $request): JsonResponse
    {
        $request->validate([
            'delivery_id' => 'required|uuid|exists:deliveries,id',
        ]);

        $delivery = Delivery::findOrFail($request->input('delivery_id'));

        // Verify driver is assigned to this delivery
        if ($delivery->assigned_driver_id !== auth()->id()) {
            return $this->errorResponse('You are not assigned to this delivery', 403);
        }

        $success = $this->trackingService->startDeliveryTracking($delivery);

        if (! $success) {
            return $this->errorResponse('Failed to start tracking', 500);
        }

        return $this->successResponse([
            'delivery_id' => $delivery->id,
            'tracking_started' => true,
            'started_at' => now()->toISOString(),
        ], 'Delivery tracking started successfully');
    }

    /**
     * Stop Delivery Tracking
     *
     * Stop real-time tracking for a delivery.
     *
     * @authenticated
     *
     * @bodyParam delivery_id string required The delivery ID to stop tracking. Example: uuid
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery tracking stopped successfully",
     *   "data": {
     *     "delivery_id": "uuid",
     *     "tracking_stopped": true,
     *     "stopped_at": "2024-01-15T11:30:00Z"
     *   }
     * }
     */
    public function stopTracking(Request $request): JsonResponse
    {
        $request->validate([
            'delivery_id' => 'required|uuid|exists:deliveries,id',
        ]);

        $delivery = Delivery::findOrFail($request->input('delivery_id'));

        // Verify driver is assigned to this delivery
        if ($delivery->assigned_driver_id !== auth()->id()) {
            return $this->errorResponse('You are not assigned to this delivery', 403);
        }

        $success = $this->trackingService->stopDeliveryTracking($delivery);

        if (! $success) {
            return $this->errorResponse('Failed to stop tracking', 500);
        }

        return $this->successResponse([
            'delivery_id' => $delivery->id,
            'tracking_stopped' => true,
            'stopped_at' => now()->toISOString(),
        ], 'Delivery tracking stopped successfully');
    }

    /**
     * Get Current Location
     *
     * Get the current location for a delivery being tracked.
     *
     * @authenticated
     *
     * @urlParam delivery_id string required The delivery ID. Example: uuid
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Current location retrieved successfully",
     *   "data": {
     *     "delivery_id": "uuid",
     *     "current_location": {
     *       "latitude": 6.5244,
     *       "longitude": 3.3792,
     *       "accuracy": 5.0,
     *       "speed": 25.5,
     *       "heading": 180.0,
     *       "timestamp": "2024-01-15T10:30:00Z"
     *     },
     *     "tracking_history": [
     *       {
     *         "latitude": 6.5200,
     *         "longitude": 3.3750,
     *         "timestamp": "2024-01-15T10:25:00Z"
     *       }
     *     ]
     *   }
     * }
     */
    public function getCurrentLocation(string $deliveryId): JsonResponse
    {
        $delivery = Delivery::findOrFail($deliveryId);

        // Verify driver is assigned to this delivery
        if ($delivery->assigned_driver_id !== auth()->id()) {
            return $this->errorResponse('You are not assigned to this delivery', 403);
        }

        $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery->id);
        $trackingHistory = $this->trackingService->getDeliveryTrackingHistory($delivery->id, 10);

        return $this->successResponse([
            'delivery_id' => $delivery->id,
            'current_location' => $currentLocation,
            'tracking_history' => $trackingHistory,
        ], 'Current location retrieved successfully');
    }

    /**
     * Get Driver's Active Deliveries
     *
     * Get all active deliveries assigned to the current driver.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Active deliveries retrieved successfully",
     *   "data": [
     *     {
     *       "delivery_id": "uuid",
     *       "order_id": "uuid",
     *       "status": "in_transit",
     *       "pickup_address": "123 Business St, Lagos",
     *       "delivery_address": "456 Customer Ave, Lagos",
     *       "estimated_delivery_time": "2024-01-15T11:15:00Z",
     *       "is_tracking_active": true
     *     }
     *   ]
     * }
     */
    public function getActiveDeliveries(): JsonResponse
    {
        $activeDeliveries = Delivery::where('assigned_driver_id', auth()->id())
            ->whereIn('status', ['accepted', 'picked_up', 'en_route'])
            ->with(['deliverable'])
            ->get()
            ->map(function ($delivery) {
                $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery->id);

                $data = [
                    'delivery_id' => $delivery->id,
                    'status' => $delivery->status->value,
                    'estimated_delivery_time' => $delivery->estimated_delivery_time?->toISOString(),
                    'is_tracking_active' => $currentLocation !== null,
                    'current_location' => $currentLocation,
                ];

                // Add order information if deliverable is an Order
                if ($delivery->deliverable_type === 'App\Models\Delivery\Order' && $delivery->deliverable) {
                    $data['order_id'] = $delivery->deliverable->id;
                    $data['order_reference'] = $delivery->deliverable->order_reference;
                }

                return $data;
            });

        return $this->successResponse(
            $activeDeliveries,
            'Active deliveries retrieved successfully'
        );
    }
}
