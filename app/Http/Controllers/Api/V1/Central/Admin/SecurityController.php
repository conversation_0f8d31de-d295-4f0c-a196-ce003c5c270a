<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\System\AuditLog;
use App\Models\System\FraudLog;
use App\Models\User\User;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Admin Security Controller
 *
 * Handles security monitoring, fraud detection, and incident management for administrators.
 * Provides comprehensive security oversight and threat analysis capabilities.
 */
class SecurityController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get security overview.
     *
     * @group Admin Security
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Security overview retrieved successfully",
     *   "data": {
     *     "overview": {
     *       "total_incidents": 25,
     *       "active_threats": 3,
     *       "blocked_ips": 15,
     *       "suspicious_activities": 45,
     *       "fraud_attempts": 8,
     *       "security_score": 85
     *     },
     *     "recent_incidents": [],
     *     "threat_levels": {
     *       "low": 20,
     *       "medium": 4,
     *       "high": 1,
     *       "critical": 0
     *     }
     *   }
     * }
     */
    public function overview(Request $request): JsonResponse
    {
        try {
            $overview = [
                'overview' => $this->getSecurityOverviewStats(),
                'recent_incidents' => $this->getRecentSecurityIncidents(),
                'threat_levels' => $this->getThreatLevelDistribution(),
                'fraud_metrics' => $this->getFraudMetrics(),
                'system_health' => $this->getSystemHealthMetrics(),
            ];

            return $this->successResponse(
                $overview,
                'Security overview retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve security overview',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve security overview');
        }
    }

    /**
     * Get fraud detection logs.
     *
     * @group Admin Security
     *
     * @authenticated
     */
    public function fraudLogs(Request $request): JsonResponse
    {
        try {
            $query = FraudLog::query()
                ->with(['user:id,first_name,last_name,email'])
                ->orderBy('created_at', 'desc');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'fraud_type',
                    'user.email',
                    'ip_address',
                ],
                'sortFields' => [
                    'created_at',
                    'fraud_type',
                    'risk_score',
                    'status',
                ],
                'filters' => [
                    'fraud_type' => ['type' => 'exact'],
                    'status' => ['type' => 'exact'],
                    'risk_level' => ['type' => 'exact'],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '>=', Carbon::parse($value));
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '<=', Carbon::parse($value)->endOfDay());
                        },
                    ],
                ],
                'message' => 'Fraud logs retrieved successfully',
                'entityName' => 'fraud_logs',
                'transformer' => [$this, 'transformFraudLog'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve fraud logs',
                $e,
                ['request_params' => $request->only(['search', 'fraud_type', 'status'])]
            );

            return $this->serverErrorResponse('Failed to retrieve fraud logs');
        }
    }

    /**
     * Get audit logs.
     *
     * @group Admin Security
     *
     * @authenticated
     */
    public function auditLogs(Request $request): JsonResponse
    {
        try {
            $query = AuditLog::query()
                ->with(['user:id,first_name,last_name,email'])
                ->orderBy('created_at', 'desc');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'event_type',
                    'user.email',
                    'ip_address',
                    'user_agent',
                ],
                'sortFields' => [
                    'created_at',
                    'event_type',
                    'severity',
                ],
                'filters' => [
                    'event_type' => ['type' => 'exact'],
                    'severity' => ['type' => 'exact'],
                    'user_id' => ['type' => 'exact'],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '>=', Carbon::parse($value));
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '<=', Carbon::parse($value)->endOfDay());
                        },
                    ],
                ],
                'message' => 'Audit logs retrieved successfully',
                'entityName' => 'audit_logs',
                'transformer' => [$this, 'transformAuditLog'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve audit logs',
                $e,
                ['request_params' => $request->only(['search', 'event_type', 'severity'])]
            );

            return $this->serverErrorResponse('Failed to retrieve audit logs');
        }
    }

    /**
     * Get suspicious users.
     *
     * @group Admin Security
     *
     * @authenticated
     */
    public function suspiciousUsers(Request $request): JsonResponse
    {
        try {
            // Users with high fraud scores or multiple fraud incidents
            $query = User::query()
                ->whereHas('fraudLogs', function ($q) {
                    $q->where('risk_score', '>=', 70)
                        ->orWhere('status', 'flagged');
                })
                ->withCount(['fraudLogs as fraud_count'])
                ->with(['fraudLogs' => function ($q) {
                    $q->orderBy('created_at', 'desc')->limit(5);
                }])
                ->orderByDesc('fraud_count');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'first_name',
                    'last_name',
                    'email',
                    'phone_number',
                ],
                'sortFields' => [
                    'fraud_count',
                    'created_at',
                    'last_login_at',
                ],
                'message' => 'Suspicious users retrieved successfully',
                'entityName' => 'suspicious_users',
                'transformer' => [$this, 'transformSuspiciousUser'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve suspicious users',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve suspicious users');
        }
    }

    /**
     * Block or unblock user.
     *
     * @group Admin Security
     *
     * @authenticated
     */
    public function blockUser(Request $request, string $userId): JsonResponse
    {
        $request->validate([
            'action' => 'required|string|in:block,unblock',
            'reason' => 'required_if:action,block|string|max:500',
            'notes' => 'sometimes|string|max:1000',
        ]);

        try {
            $user = User::find($userId);
            if (! $user) {
                return $this->notFoundResponse('User not found');
            }

            $action = $request->input('action');
            $isBlocking = $action === 'block';

            DB::transaction(function () use ($user, $isBlocking, $request, $action) {
                $user->update([
                    'is_active' => ! $isBlocking,
                    'blocked_at' => $isBlocking ? now() : null,
                    'blocked_reason' => $isBlocking ? $request->input('reason') : null,
                ]);

                // Log the security action
                $this->loggingService->logActivity(
                    $isBlocking ? 'user_blocked' : 'user_unblocked',
                    $isBlocking ? 'User blocked by admin' : 'User unblocked by admin',
                    [
                        'user_id' => $user->id,
                        'action' => $action,
                        'reason' => $request->input('reason'),
                        'notes' => $request->input('notes'),
                        'blocked_by' => auth()->id(),
                    ]
                );
            });

            return $this->successResponse([
                'user_id' => $user->id,
                'action' => $action,
                'is_active' => $user->is_active,
                'blocked_at' => $user->blocked_at?->toISOString(),
            ], "User {$action}ed successfully");

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to block/unblock user',
                $e,
                ['user_id' => $userId, 'action' => $request->input('action')]
            );

            return $this->serverErrorResponse('Failed to update user status');
        }
    }

    /**
     * Get security analytics.
     *
     * @group Admin Security
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $period = $request->input('period', 'month');
            $periodDates = $this->getPeriodDates($period);

            $analytics = [
                'fraud_trends' => $this->getFraudTrends($periodDates),
                'security_incidents' => $this->getSecurityIncidentTrends($periodDates),
                'threat_analysis' => $this->getThreatAnalysis($periodDates),
                'user_behavior' => $this->getUserBehaviorAnalysis($periodDates),
                'system_vulnerabilities' => $this->getSystemVulnerabilities(),
            ];

            return $this->successResponse(
                $analytics,
                'Security analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve security analytics',
                $e,
                ['request_params' => $request->only(['period'])]
            );

            return $this->serverErrorResponse('Failed to retrieve security analytics');
        }
    }

    /**
     * Transform fraud log for API response.
     */
    public function transformFraudLog(FraudLog $fraudLog): array
    {
        return [
            'id' => $fraudLog->id,
            'fraud_type' => $fraudLog->fraud_type,
            'risk_score' => $fraudLog->risk_score,
            'risk_level' => $fraudLog->risk_level,
            'status' => $fraudLog->status,
            'ip_address' => $fraudLog->ip_address,
            'user_agent' => $fraudLog->user_agent,
            'details' => $fraudLog->details,
            'created_at' => $fraudLog->created_at->toISOString(),
            'user' => $fraudLog->user ? [
                'id' => $fraudLog->user->id,
                'name' => $fraudLog->user->first_name.' '.$fraudLog->user->last_name,
                'email' => $fraudLog->user->email,
            ] : null,
        ];
    }

    /**
     * Transform audit log for API response.
     */
    public function transformAuditLog(AuditLog $auditLog): array
    {
        return [
            'id' => $auditLog->id,
            'event_type' => $auditLog->event_type,
            'severity' => $auditLog->severity,
            'description' => $auditLog->description,
            'ip_address' => $auditLog->ip_address,
            'user_agent' => $auditLog->user_agent,
            'metadata' => $auditLog->metadata,
            'created_at' => $auditLog->created_at->toISOString(),
            'user' => $auditLog->user ? [
                'id' => $auditLog->user->id,
                'name' => $auditLog->user->first_name.' '.$auditLog->user->last_name,
                'email' => $auditLog->user->email,
            ] : null,
        ];
    }

    /**
     * Transform suspicious user for API response.
     */
    public function transformSuspiciousUser(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->first_name.' '.$user->last_name,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'is_active' => $user->is_active,
            'blocked_at' => $user->blocked_at?->toISOString(),
            'blocked_reason' => $user->blocked_reason,
            'fraud_count' => $user->fraud_count ?? 0,
            'last_login_at' => $user->last_login_at?->toISOString(),
            'created_at' => $user->created_at->toISOString(),
            'recent_fraud_logs' => $user->fraudLogs?->map([$this, 'transformFraudLog'])->toArray() ?? [],
        ];
    }

    /**
     * Get security overview statistics.
     */
    private function getSecurityOverviewStats(): array
    {
        return [
            'total_incidents' => AuditLog::where('severity', '>=', 'medium')->count(),
            'active_threats' => FraudLog::where('status', 'active')->count(),
            'blocked_users' => User::whereNotNull('blocked_at')->count(),
            'suspicious_activities' => FraudLog::where('risk_score', '>=', 50)->count(),
            'fraud_attempts' => FraudLog::where('fraud_type', 'payment_fraud')->count(),
            'security_score' => $this->calculateSecurityScore(),
        ];
    }

    /**
     * Get recent security incidents.
     */
    private function getRecentSecurityIncidents(): array
    {
        return AuditLog::where('severity', '>=', 'medium')
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map([$this, 'transformAuditLog'])
            ->toArray();
    }

    /**
     * Get threat level distribution.
     */
    private function getThreatLevelDistribution(): array
    {
        return FraudLog::select('risk_level')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('risk_level')
            ->pluck('count', 'risk_level')
            ->toArray();
    }

    /**
     * Get fraud metrics.
     */
    private function getFraudMetrics(): array
    {
        $today = now()->startOfDay();
        $week = now()->subWeek();

        return [
            'fraud_attempts_today' => FraudLog::where('created_at', '>=', $today)->count(),
            'fraud_attempts_this_week' => FraudLog::where('created_at', '>=', $week)->count(),
            'average_risk_score' => FraudLog::avg('risk_score') ?? 0,
            'blocked_transactions' => FraudLog::where('status', 'blocked')->count(),
        ];
    }

    /**
     * Get system health metrics.
     */
    private function getSystemHealthMetrics(): array
    {
        return [
            'uptime_percentage' => 99.9, // This would come from monitoring service
            'response_time_avg' => 150, // milliseconds
            'error_rate' => 0.1, // percentage
            'active_sessions' => $this->getActiveSessionsCount(),
        ];
    }

    /**
     * Calculate overall security score.
     */
    private function calculateSecurityScore(): int
    {
        $baseScore = 100;

        // Deduct points for recent incidents
        $recentIncidents = AuditLog::where('created_at', '>=', now()->subWeek())
            ->where('severity', '>=', 'medium')
            ->count();

        $fraudAttempts = FraudLog::where('created_at', '>=', now()->subWeek())
            ->where('risk_score', '>=', 70)
            ->count();

        $deductions = ($recentIncidents * 2) + ($fraudAttempts * 3);

        return max(0, min(100, $baseScore - $deductions));
    }

    /**
     * Get active sessions count.
     */
    private function getActiveSessionsCount(): int
    {
        // This would integrate with session storage (Redis/Database)
        return DB::table('sessions')->count();
    }

    /**
     * Get period dates for analytics.
     */
    private function getPeriodDates(string $period): array
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }

    /**
     * Placeholder methods for analytics.
     */
    private function getFraudTrends(array $periodDates): array
    {
        return ['fraud_by_day' => [], 'fraud_by_type' => []];
    }

    private function getSecurityIncidentTrends(array $periodDates): array
    {
        return ['incidents_by_day' => [], 'incidents_by_severity' => []];
    }

    private function getThreatAnalysis(array $periodDates): array
    {
        return ['threat_sources' => [], 'attack_patterns' => []];
    }

    private function getUserBehaviorAnalysis(array $periodDates): array
    {
        return ['suspicious_patterns' => [], 'user_risk_distribution' => []];
    }

    private function getSystemVulnerabilities(): array
    {
        return ['open_vulnerabilities' => 0, 'patched_vulnerabilities' => 0];
    }
}
