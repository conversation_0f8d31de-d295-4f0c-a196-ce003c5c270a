<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\ComplianceManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Compliance Controller
 *
 * Handles comprehensive regulatory compliance management for administrators.
 * Manages compliance rules, monitoring, reporting, and data subject requests.
 */
class ComplianceController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly ComplianceManagementService $complianceService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all compliance rules with filtering.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'regulation' => 'sometimes|string|in:GDPR,CCPA,PCI_DSS,HIPAA,SOX',
                'status' => 'sometimes|string|in:active,inactive,draft',
                'category' => 'sometimes|string|in:data_privacy,financial,security,operational',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $rules = $this->complianceService->getComplianceRules($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedRules = array_slice($rules, $offset, $perPage);
            $total = count($rules);

            return $this->successResponse([
                'data' => array_map([$this, 'transformComplianceRule'], $paginatedRules),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Compliance rules retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve compliance rules',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve compliance rules');
        }
    }

    /**
     * Create a new compliance rule.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'regulation' => 'required|string|in:GDPR,CCPA,PCI_DSS,HIPAA,SOX',
                'category' => 'required|string|in:data_privacy,financial,security,operational',
                'severity' => 'sometimes|string|in:low,medium,high,critical',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'rule_definition' => 'required|string|max:2000',
                'enforcement_action' => 'sometimes|string|in:alert,block,auto_remediate,auto_delete',
                'metadata' => 'sometimes|array',
            ]);

            $rule = $this->complianceService->createComplianceRule($validated);

            return $this->successResponse(
                $this->transformComplianceRule($rule),
                'Compliance rule created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create compliance rule',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create compliance rule');
        }
    }

    /**
     * Get compliance analytics and insights.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'regulation' => 'sometimes|string|in:GDPR,CCPA,PCI_DSS,HIPAA,SOX',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->complianceService->getComplianceAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Compliance analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve compliance analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve compliance analytics');
        }
    }

    /**
     * Run compliance check.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function runCheck(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:full,incremental,targeted',
                'scope' => 'sometimes|string|in:platform,tenant,specific',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'rules' => 'sometimes|array',
                'rules.*' => 'string',
            ]);

            $check = $this->complianceService->runComplianceCheck($validated);

            return $this->successResponse(
                $check,
                'Compliance check completed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to run compliance check',
                $e,
                ['admin_id' => auth()->id(), 'options' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to run compliance check');
        }
    }

    /**
     * Generate compliance report.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:comprehensive,summary,violation_report,dsr_report',
                'regulation' => 'sometimes|string|in:GDPR,CCPA,PCI_DSS,HIPAA,SOX',
                'period_start' => 'sometimes|date',
                'period_end' => 'sometimes|date|after_or_equal:period_start',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'format' => 'sometimes|string|in:json,pdf,csv',
            ]);

            $report = $this->complianceService->generateComplianceReport($validated);

            return $this->successResponse(
                $report,
                'Compliance report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate compliance report',
                $e,
                ['admin_id' => auth()->id(), 'config' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to generate compliance report');
        }
    }

    /**
     * Handle data subject request.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function handleDataSubjectRequest(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|in:access,deletion,portability,rectification',
                'subject_email' => 'required|email',
                'subject_id' => 'sometimes|uuid',
                'regulation' => 'sometimes|string|in:GDPR,CCPA',
                'metadata' => 'sometimes|array',
            ]);

            $dataRequest = $this->complianceService->handleDataSubjectRequest($validated);

            return $this->successResponse(
                $dataRequest,
                'Data subject request processed successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to handle data subject request',
                $e,
                ['admin_id' => auth()->id(), 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to handle data subject request');
        }
    }

    /**
     * Update compliance rule.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function update(Request $request, string $ruleId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'severity' => 'sometimes|string|in:low,medium,high,critical',
                'rule_definition' => 'sometimes|string|max:2000',
                'enforcement_action' => 'sometimes|string|in:alert,block,auto_remediate,auto_delete',
                'status' => 'sometimes|string|in:active,inactive,draft',
            ]);

            $rule = $this->complianceService->updateComplianceRule($ruleId, $validated);

            return $this->successResponse(
                $this->transformComplianceRule($rule),
                'Compliance rule updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Compliance rule not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update compliance rule',
                $e,
                ['rule_id' => $ruleId, 'updates' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update compliance rule');
        }
    }

    /**
     * Delete compliance rule.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function destroy(Request $request, string $ruleId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->complianceService->deleteComplianceRule(
                $ruleId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Compliance rule deleted successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Compliance rule not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete compliance rule',
                $e,
                ['rule_id' => $ruleId]
            );

            return $this->serverErrorResponse('Failed to delete compliance rule');
        }
    }

    /**
     * Bulk compliance operations.
     *
     * @group Admin Compliance
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'rule_ids' => 'required|array|min:1',
                'rule_ids.*' => 'string',
                'action' => 'required|string|in:activate,deactivate,delete',
                'reason' => 'sometimes|string|max:500',
            ]);

            $results = $this->complianceService->bulkComplianceOperations(
                $validated['rule_ids'],
                $validated['action'],
                array_filter([
                    'reason' => $validated['reason'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk compliance operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk compliance operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform compliance rule for API response.
     */
    public function transformComplianceRule(array $rule): array
    {
        return [
            'id' => $rule['id'],
            'name' => $rule['name'],
            'description' => $rule['description'],
            'regulation' => $rule['regulation'],
            'category' => $rule['category'],
            'severity' => $rule['severity'],
            'tenant_id' => $rule['tenant_id'],
            'rule_definition' => $rule['rule_definition'],
            'enforcement_action' => $rule['enforcement_action'],
            'status' => $rule['status'],
            'created_at' => $rule['created_at'],
            'created_by' => $rule['created_by'],
            'last_checked' => $rule['last_checked'],
            'violations_count' => $rule['violations_count'],
            'metadata' => $rule['metadata'],
        ];
    }
}
