<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Business\PromotionManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Promotion Controller
 *
 * Handles comprehensive promotion and discount management for administrators.
 * Manages promotion campaigns, discount codes, usage tracking, and fraud prevention.
 */
class PromoController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly PromotionManagementService $promotionService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all promotions with filtering and analytics.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'country_id' => 'sometimes|uuid|exists:countries,id',
                'type' => 'sometimes|string|in:percentage_discount,fixed_discount,free_delivery,buy_x_get_y',
                'status' => 'sometimes|string|in:active,expired,scheduled,inactive',
                'is_active' => 'sometimes|boolean',
                'search' => 'sometimes|string|max:255',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'sort_by' => 'sometimes|string|in:name,created_at,expires_at,usage_count',
                'sort_direction' => 'sometimes|string|in:asc,desc',
            ]);

            $query = $this->promotionService->getPromotions($validated);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'code',
                    'description',
                ],
                'sortFields' => [
                    'name',
                    'created_at',
                    'expires_at',
                    'usages_count',
                    'discount_value',
                ],
                'filters' => [
                    'business_id' => ['type' => 'exact'],
                    'country_id' => ['type' => 'exact'],
                    'type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Promotions retrieved successfully',
                'entityName' => 'promotions',
                'transformer' => [$this, 'transformPromotion'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve promotions',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve promotions');
        }
    }

    /**
     * Create a new promotion.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'country_id' => 'sometimes|uuid|exists:countries,id',
                'name' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'code' => 'sometimes|string|max:50|unique:promotions,code',
                'type' => 'required|string|in:percentage_discount,fixed_discount,free_delivery,buy_x_get_y',
                'discount_value' => 'required_unless:type,free_delivery|numeric|min:0',
                'min_order_value' => 'sometimes|numeric|min:0',
                'max_discount_amount' => 'sometimes|numeric|min:0',
                'currency' => 'sometimes|string|size:3',
                'usage_limit_per_user' => 'sometimes|integer|min:1',
                'usage_limit_total' => 'sometimes|integer|min:1',
                'starts_at' => 'sometimes|date|after_or_equal:now',
                'expires_at' => 'sometimes|date|after:starts_at',
                'is_active' => 'sometimes|boolean',
            ]);

            $promotion = $this->promotionService->createPromotion($validated);

            return $this->successResponse(
                $this->transformPromotion($promotion),
                'Promotion created successfully',
                201
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create promotion',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create promotion');
        }
    }

    /**
     * Get a specific promotion.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function show(string $promotionId): JsonResponse
    {
        try {
            $promotion = \App\Models\Business\Promotion::with(['business:id,name', 'country:id,name'])
                ->withCount(['usages'])
                ->findOrFail($promotionId);

            return $this->successResponse(
                $this->transformPromotion($promotion),
                'Promotion retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Promotion not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve promotion',
                $e,
                ['promotion_id' => $promotionId]
            );

            return $this->serverErrorResponse('Failed to retrieve promotion');
        }
    }

    /**
     * Update a promotion.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function update(Request $request, string $promotionId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'code' => 'sometimes|string|max:50|unique:promotions,code,'.$promotionId,
                'type' => 'sometimes|string|in:percentage_discount,fixed_discount,free_delivery,buy_x_get_y',
                'discount_value' => 'sometimes|numeric|min:0',
                'min_order_value' => 'sometimes|numeric|min:0',
                'max_discount_amount' => 'sometimes|numeric|min:0',
                'currency' => 'sometimes|string|size:3',
                'usage_limit_per_user' => 'sometimes|integer|min:1',
                'usage_limit_total' => 'sometimes|integer|min:1',
                'starts_at' => 'sometimes|date',
                'expires_at' => 'sometimes|date|after:starts_at',
                'is_active' => 'sometimes|boolean',
            ]);

            $promotion = $this->promotionService->updatePromotion($promotionId, $validated);

            return $this->successResponse(
                $this->transformPromotion($promotion),
                'Promotion updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Promotion not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update promotion',
                $e,
                ['promotion_id' => $promotionId, 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update promotion');
        }
    }

    /**
     * Delete a promotion.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function destroy(Request $request, string $promotionId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->promotionService->deletePromotion(
                $promotionId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Promotion deleted successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Promotion not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete promotion',
                $e,
                ['promotion_id' => $promotionId]
            );

            return $this->serverErrorResponse('Failed to delete promotion');
        }
    }

    /**
     * Toggle promotion status (activate/deactivate).
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function toggleStatus(Request $request, string $promotionId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'is_active' => 'required|boolean',
                'reason' => 'sometimes|string|max:500',
            ]);

            $promotion = $this->promotionService->togglePromotionStatus(
                $promotionId,
                $validated['is_active'],
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                $this->transformPromotion($promotion),
                'Promotion status updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Promotion not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to toggle promotion status',
                $e,
                ['promotion_id' => $promotionId]
            );

            return $this->serverErrorResponse('Failed to update promotion status');
        }
    }

    /**
     * Get promotion analytics and insights.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->promotionService->getPromotionAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Promotion analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve promotion analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve promotion analytics');
        }
    }

    /**
     * Get promotion usage tracking.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function usage(string $promotionId): JsonResponse
    {
        try {
            $usage = $this->promotionService->getPromotionUsage($promotionId);

            return $this->successResponse(
                $usage,
                'Promotion usage retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Promotion not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve promotion usage',
                $e,
                ['promotion_id' => $promotionId]
            );

            return $this->serverErrorResponse('Failed to retrieve promotion usage');
        }
    }

    /**
     * Detect promotion fraud.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function fraudDetection(string $promotionId): JsonResponse
    {
        try {
            $fraudAnalysis = $this->promotionService->detectPromotionFraud($promotionId);

            return $this->successResponse(
                $fraudAnalysis,
                'Promotion fraud analysis completed'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Promotion not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to analyze promotion fraud',
                $e,
                ['promotion_id' => $promotionId]
            );

            return $this->serverErrorResponse('Failed to analyze promotion fraud');
        }
    }

    /**
     * Bulk operations on promotions.
     *
     * @group Admin Promotions
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'promotion_ids' => 'required|array|min:1',
                'promotion_ids.*' => 'uuid|exists:promotions,id',
                'action' => 'required|string|in:activate,deactivate,extend_expiry,delete',
                'reason' => 'sometimes|string|max:500',
                'new_expiry_date' => 'required_if:action,extend_expiry|date|after:now',
                'is_featured' => 'sometimes|boolean',
            ]);

            $results = $this->promotionService->bulkPromotionOperations(
                $validated['promotion_ids'],
                $validated['action'],
                array_filter([
                    'reason' => $validated['reason'] ?? null,
                    'new_expiry_date' => $validated['new_expiry_date'] ?? null,
                    'is_featured' => $validated['is_featured'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk promotion operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk promotion operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform promotion for API response.
     */
    public function transformPromotion($promotion): array
    {
        return [
            'id' => $promotion->id,
            'tenant_id' => $promotion->tenant_id,
            'business' => $promotion->business ? [
                'id' => $promotion->business->id,
                'name' => $promotion->business->name,
            ] : null,
            'country' => $promotion->country ? [
                'id' => $promotion->country->id,
                'name' => $promotion->country->name,
            ] : null,
            'name' => $promotion->name,
            'description' => $promotion->description,
            'code' => $promotion->code,
            'type' => $promotion->type->value,
            'discount_value' => $promotion->discount_value,
            'formatted_discount' => $promotion->getFormattedDiscount(),
            'min_order_value' => $promotion->min_order_value,
            'max_discount_amount' => $promotion->max_discount_amount,
            'currency' => $promotion->currency,
            'usage_limit_per_user' => $promotion->usage_limit_per_user,
            'usage_limit_total' => $promotion->usage_limit_total,
            'usage_count' => $promotion->usages_count ?? 0,
            'starts_at' => $promotion->starts_at,
            'expires_at' => $promotion->expires_at,
            'is_active' => $promotion->is_active,
            'is_currently_valid' => $promotion->isCurrentlyValid(),
            'created_at' => $promotion->created_at,
            'updated_at' => $promotion->updated_at,
        ];
    }
}
