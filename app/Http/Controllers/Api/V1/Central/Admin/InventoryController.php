<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Business\InventoryManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Inventory Controller
 *
 * Handles comprehensive cross-tenant inventory management for administrators.
 * Manages stock oversight, inventory analytics, and bulk operations.
 */
class InventoryController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly InventoryManagementService $inventoryService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get inventory overview with filtering.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'category_id' => 'sometimes|uuid|exists:categories,id',
                'stock_status' => 'sometimes|string|in:in_stock,low_stock,out_of_stock,unlimited',
                'is_available' => 'sometimes|boolean',
                'search' => 'sometimes|string|max:255',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'sort_by' => 'sometimes|string|in:name,quantity,price,created_at',
                'sort_direction' => 'sometimes|string|in:asc,desc',
            ]);

            $query = $this->inventoryService->getInventoryOverview($validated);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'sku',
                    'barcode',
                ],
                'sortFields' => [
                    'name',
                    'quantity',
                    'price',
                    'created_at',
                ],
                'filters' => [
                    'business_id' => ['type' => 'exact'],
                    'tenant_id' => ['type' => 'exact'],
                    'category_id' => ['type' => 'exact'],
                    'is_available' => ['type' => 'boolean'],
                ],
                'message' => 'Inventory overview retrieved successfully',
                'entityName' => 'products',
                'transformer' => [$this, 'transformInventoryItem'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve inventory overview',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve inventory overview');
        }
    }

    /**
     * Get comprehensive inventory analytics.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->inventoryService->getInventoryAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Inventory analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve inventory analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve inventory analytics');
        }
    }

    /**
     * Get inventory movements with filtering.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function movements(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'product_id' => 'sometimes|uuid|exists:products,id',
                'type' => 'sometimes|string',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $query = $this->inventoryService->getInventoryMovements($validated);

            return $this->handleQuery($query, $request, [
                'sortFields' => [
                    'created_at',
                    'quantity_change',
                ],
                'filters' => [
                    'business_id' => ['type' => 'exact'],
                    'tenant_id' => ['type' => 'exact'],
                    'product_id' => ['type' => 'exact'],
                    'type' => ['type' => 'exact'],
                ],
                'message' => 'Inventory movements retrieved successfully',
                'entityName' => 'movements',
                'transformer' => [$this, 'transformInventoryMovement'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve inventory movements',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve inventory movements');
        }
    }

    /**
     * Get low stock alerts.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function lowStockAlerts(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
            ]);

            $alerts = $this->inventoryService->getLowStockAlerts($validated);

            return $this->successResponse(
                $alerts,
                'Low stock alerts retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve low stock alerts',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve low stock alerts');
        }
    }

    /**
     * Get out of stock products.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function outOfStock(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
            ]);

            $outOfStock = $this->inventoryService->getOutOfStockProducts($validated);

            return $this->successResponse(
                $outOfStock,
                'Out of stock products retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve out of stock products',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve out of stock products');
        }
    }

    /**
     * Bulk inventory operations.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'product_ids' => 'required|array|min:1',
                'product_ids.*' => 'uuid|exists:products,id',
                'action' => 'required|string|in:update_stock,increase_stock,decrease_stock,enable_product,disable_product',
                'new_stock' => 'required_if:action,update_stock|integer|min:0',
                'quantity' => 'required_if:action,increase_stock,decrease_stock|integer|min:1',
                'reason' => 'sometimes|string|max:500',
            ]);

            $results = $this->inventoryService->bulkInventoryOperations(
                $validated['product_ids'],
                $validated['action'],
                array_filter([
                    'new_stock' => $validated['new_stock'] ?? null,
                    'quantity' => $validated['quantity'] ?? null,
                    'reason' => $validated['reason'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk inventory operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk inventory operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform inventory item for API response.
     */
    public function transformInventoryItem($product): array
    {
        return [
            'id' => $product->id,
            'tenant_id' => $product->tenant_id,
            'business' => $product->business ? [
                'id' => $product->business->id,
                'name' => $product->business->business_name,
            ] : null,
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->name,
            ] : null,
            'name' => $product->name,
            'sku' => $product->sku,
            'barcode' => $product->barcode,
            'current_stock' => $product->quantity,
            'stock_status' => $this->getStockStatus($product->quantity),
            'price' => $product->price,
            'cost_price' => $product->cost_price,
            'inventory_value' => $product->quantity > 0 && $product->quantity !== -1
                ? round($product->quantity * $product->price, 2)
                : 0,
            'is_available' => $product->is_available,
            'created_at' => $product->created_at,
            'updated_at' => $product->updated_at,
        ];
    }

    /**
     * Transform inventory movement for API response.
     */
    public function transformInventoryMovement($movement): array
    {
        return [
            'id' => $movement->id,
            'tenant_id' => $movement->tenant_id,
            'business' => $movement->business ? [
                'id' => $movement->business->id,
                'name' => $movement->business->business_name,
            ] : null,
            'product' => $movement->product ? [
                'id' => $movement->product->id,
                'name' => $movement->product->name,
                'sku' => $movement->product->sku,
            ] : null,
            'type' => $movement->type->value,
            'quantity_change' => $movement->quantity_change,
            'current_stock' => $movement->current_stock,
            'source_type' => $movement->source_type,
            'source_id' => $movement->source_id,
            'notes' => $movement->notes,
            'created_at' => $movement->created_at,
        ];
    }

    /**
     * Generate business inventory report.
     *
     * @group Admin Inventory
     *
     * @authenticated
     */
    public function businessReport(string $businessId): JsonResponse
    {
        try {
            $report = $this->inventoryService->generateBusinessInventoryReport($businessId);

            return $this->successResponse(
                $report,
                'Business inventory report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate business inventory report',
                $e,
                ['business_id' => $businessId]
            );

            return $this->serverErrorResponse('Failed to generate business inventory report');
        }
    }

    /**
     * Get stock status based on quantity.
     */
    private function getStockStatus(int $quantity): string
    {
        if ($quantity === -1) {
            return 'unlimited';
        } elseif ($quantity <= 0) {
            return 'out_of_stock';
        } elseif ($quantity <= 10) {
            return 'low_stock';
        } else {
            return 'in_stock';
        }
    }
}
