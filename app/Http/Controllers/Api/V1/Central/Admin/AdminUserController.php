<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Events\User\AccountSuspended;
use App\Http\Controllers\Controller;
use App\Models\User\User;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Central Admin User Management Controller
 *
 * Handles cross-tenant user management for platform administrators.
 * Provides comprehensive user oversight, management, and administration.
 */
class AdminUserController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all users across tenants.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search users by name or email. Example: john
     * @queryParam user_type string Filter by user type. Example: business_owner
     * @queryParam tenant_id string Filter by tenant ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam is_active boolean Filter by active status. Example: true
     * @queryParam email_verified boolean Filter by email verification. Example: true
     * @queryParam sort_by string Sort by field. Example: created_at
     * @queryParam sort_direction string Sort direction (asc, desc). Example: desc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Users retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "first_name": "John",
     *         "last_name": "Doe",
     *         "email": "<EMAIL>",
     *         "phone_number": "***********",
     *         "user_type": "business_owner",
     *         "is_active": true,
     *         "email_verified_at": "2024-01-15T10:30:00Z",
     *         "phone_verified_at": "2024-01-15T10:30:00Z",
     *         "tenant": {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *           "name": "Example Business"
     *         },
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 150
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Build query with relationships
            $query = User::with(['tenant:id,name,tenant_type', 'roles', 'abilities']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'first_name',
                    'last_name',
                    'email',
                    'phone_number',
                    'tenant.name',
                ],
                'sortFields' => [
                    'first_name',
                    'last_name',
                    'email',
                    'user_type',
                    'created_at',
                    'updated_at',
                ],
                'filters' => [
                    'user_type' => ['type' => 'exact'],
                    'tenant_id' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'email_verified' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                                $query->whereNotNull('email_verified_at');
                            } else {
                                $query->whereNull('email_verified_at');
                            }
                        },
                    ],
                ],
                'message' => 'Users retrieved successfully',
                'entityName' => 'users',
                'transformer' => [$this, 'transformUser'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve users',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'request_params' => $request->only(['search', 'user_type', 'tenant_id', 'is_active']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve users');
        }
    }

    /**
     * Get specific user details.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "first_name": "John",
     *     "last_name": "Doe",
     *     "email": "<EMAIL>",
     *     "phone_number": "***********",
     *     "user_type": "business_owner",
     *     "is_active": true,
     *     "email_verified_at": "2024-01-15T10:30:00Z",
     *     "phone_verified_at": "2024-01-15T10:30:00Z",
     *     "tenant": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "Example Business",
     *       "tenant_type": "business"
     *     },
     *     "roles": ["business-owner"],
     *     "abilities": ["manage-business"],
     *     "addresses": [],
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $user): JsonResponse
    {
        try {
            $query = User::query();

            return $this->handleShow($query, $user, [
                'with' => [
                    'tenant:id,name,tenant_type,status',
                    'roles',
                    'abilities',
                    'addresses',
                    'customerProfile',
                    'businessProfile',
                    'deliveryProviderProfile',
                ],
                'transformer' => fn ($model) => $this->transformUser($model, true),
                'message' => 'User retrieved successfully',
                'notFoundMessage' => 'User not found',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user details',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve user details');
        }
    }

    /**
     * Update user details.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam first_name string User's first name. Example: John
     * @bodyParam last_name string User's last name. Example: Doe
     * @bodyParam email string User's email address. Example: <EMAIL>
     * @bodyParam phone_number string User's phone number. Example: ***********
     * @bodyParam is_active boolean User's active status. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "first_name": "John",
     *     "last_name": "Doe",
     *     "email": "<EMAIL>",
     *     "phone_number": "***********",
     *     "is_active": true
     *   }
     * }
     */
    public function update(Request $request, string $user): JsonResponse
    {
        $request->validate([
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255|unique:users,email,'.$user,
            'phone_number' => 'sometimes|string|max:20',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $userModel = User::findOrFail($user);

            $updateData = $request->only([
                'first_name', 'last_name', 'email', 'phone_number', 'is_active',
            ]);

            // Remove empty values
            $updateData = array_filter($updateData, function ($value) {
                return $value !== null && $value !== '';
            });

            if (empty($updateData)) {
                return $this->errorResponse(
                    'No valid data provided for update',
                    422
                );
            }

            $userModel->update($updateData);

            $this->loggingService->logInfo(
                'User updated by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                    'updated_fields' => array_keys($updateData),
                ]
            );

            return $this->successResponse(
                $this->transformUser($userModel->fresh()),
                'User updated successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update user',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                    'update_data' => $updateData ?? [],
                ]
            );

            return $this->serverErrorResponse('Failed to update user');
        }
    }

    /**
     * Activate a user.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User activated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "is_active": true
     *   }
     * }
     */
    public function activate(string $user): JsonResponse
    {
        return $this->updateUserStatus($user, true, 'User activated successfully');
    }

    /**
     * Suspend a user.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User suspended successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "is_active": false
     *   }
     * }
     */
    public function suspend(string $user): JsonResponse
    {
        return $this->updateUserStatus($user, false, 'User suspended successfully');
    }

    /**
     * Verify user's email.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User email verified successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "email_verified_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function verifyEmail(string $user): JsonResponse
    {
        try {
            $userModel = User::findOrFail($user);

            if ($userModel->email_verified_at) {
                return $this->errorResponse(
                    'User email is already verified',
                    422
                );
            }

            $userModel->update([
                'email_verified_at' => now(),
            ]);

            $this->loggingService->logInfo(
                'User email verified by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->successResponse(
                [
                    'id' => $userModel->id,
                    'email_verified_at' => $userModel->email_verified_at->toISOString(),
                ],
                'User email verified successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify user email',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->serverErrorResponse('Failed to verify user email');
        }
    }

    /**
     * Verify user's phone.
     *
     * @group Admin User Management
     *
     * @authenticated
     *
     * @urlParam user string required User ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "User phone verified successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "phone_verified_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function verifyPhone(string $user): JsonResponse
    {
        try {
            $userModel = User::findOrFail($user);

            if ($userModel->phone_verified_at) {
                return $this->errorResponse(
                    'User phone is already verified',
                    422
                );
            }

            $userModel->update([
                'phone_verified_at' => now(),
            ]);

            $this->loggingService->logInfo(
                'User phone verified by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->successResponse(
                [
                    'id' => $userModel->id,
                    'phone_verified_at' => $userModel->phone_verified_at->toISOString(),
                ],
                'User phone verified successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify user phone',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $user,
                ]
            );

            return $this->serverErrorResponse('Failed to verify user phone');
        }
    }

    // Helper Methods

    /**
     * Update user status (activate/suspend).
     */
    private function updateUserStatus(string $userId, bool $isActive, string $message): JsonResponse
    {
        try {
            $userModel = User::findOrFail($userId);

            $userModel->update(['is_active' => $isActive]);

            // Dispatch AccountSuspended event if user is being suspended
            if (! $isActive) {
                AccountSuspended::dispatch(
                    $userModel,
                    'Administrative action', // reason
                    'pending_review', // suspensionType
                    auth()->user(), // suspendedBy
                    null, // suspendedUntil
                    'Contact support for more information about your account suspension.', // appealProcess
                    ['admin_action' => true, 'admin_id' => auth()->id()] // violationDetails
                );
            }

            $this->loggingService->logInfo(
                'User status updated by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $userId,
                    'new_status' => $isActive ? 'active' : 'suspended',
                ]
            );

            return $this->successResponse(
                [
                    'id' => $userModel->id,
                    'is_active' => $userModel->is_active,
                ],
                $message
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'User not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update user status',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'target_user_id' => $userId,
                    'intended_status' => $isActive ? 'active' : 'suspended',
                ]
            );

            return $this->serverErrorResponse('Failed to update user status');
        }
    }

    /**
     * Transform user for API response.
     */
    private function transformUser(User $user, bool $detailed = false): array
    {
        $data = [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'user_type' => $user->user_type->value,
            'is_active' => $user->is_active,
            'email_verified_at' => $user->email_verified_at?->toISOString(),
            'phone_verified_at' => $user->phone_verified_at?->toISOString(),
            'tenant' => $user->tenant ? [
                'id' => $user->tenant->id,
                'name' => $user->tenant->name,
                'tenant_type' => $user->tenant->tenant_type->value ?? null,
            ] : null,
            'created_at' => $user->created_at->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'roles' => $user->roles->pluck('name')->toArray(),
                'abilities' => $user->abilities->pluck('name')->toArray(),
                'addresses' => $user->addresses->map(function ($address) {
                    return [
                        'id' => $address->id,
                        'type' => $address->type,
                        'address_line_1' => $address->address_line_1,
                        'city' => $address->city,
                        'state' => $address->state,
                        'country' => $address->country,
                    ];
                })->toArray(),
                'updated_at' => $user->updated_at->toISOString(),
            ]);
        }

        return $data;
    }
}
