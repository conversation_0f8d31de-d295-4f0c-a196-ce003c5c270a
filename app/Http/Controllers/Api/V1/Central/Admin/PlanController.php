<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Admin\CreatePlanRequest;
use App\Http\Requests\Api\V1\Admin\UpdatePlanRequest;
use App\Models\Financial\SubscriptionPlan;
use App\Services\Financial\PlanManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Plan Controller
 *
 * Handles subscription plan management for administrators.
 * Provides CRUD operations for subscription plans, pricing, and features.
 */
class PlanController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly PlanManagementService $planService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all subscription plans.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = SubscriptionPlan::query()
                ->with(['planPrices', 'planFeatures.feature']);

            return $this->handleQuery($query, $request, [
                'searchFields' => ['name', 'description'],
                'sortFields' => ['name', 'target_type', 'created_at'],
                'filters' => [
                    'target_type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Subscription plans retrieved successfully',
                'entityName' => 'plans',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription plans',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve subscription plans');
        }
    }

    /**
     * Get specific subscription plan.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function show(Request $request, string $planId): JsonResponse
    {
        try {
            $plan = $this->planService->getPlanWithDetails($planId);

            return $this->successResponse(
                $plan,
                'Subscription plan retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription plan',
                $e,
                ['plan_id' => $planId]
            );

            return $this->serverErrorResponse('Failed to retrieve subscription plan');
        }
    }

    /**
     * Create new subscription plan.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function store(CreatePlanRequest $request): JsonResponse
    {
        try {
            $plan = $this->planService->createPlan($request->validated());

            return $this->createdResponse(
                $plan,
                'Subscription plan created successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create subscription plan',
                $e,
                ['request_data' => $request->validated()]
            );

            return $this->serverErrorResponse('Failed to create subscription plan');
        }
    }

    /**
     * Update subscription plan.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function update(UpdatePlanRequest $request, string $planId): JsonResponse
    {
        try {
            $plan = $this->planService->updatePlan($planId, $request->validated());

            return $this->successResponse(
                $plan,
                'Subscription plan updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update subscription plan',
                $e,
                ['plan_id' => $planId, 'request_data' => $request->validated()]
            );

            return $this->serverErrorResponse('Failed to update subscription plan');
        }
    }

    /**
     * Delete subscription plan.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function destroy(Request $request, string $planId): JsonResponse
    {
        try {
            $this->planService->deletePlan($planId);

            return $this->successResponse(
                null,
                'Subscription plan deleted successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete subscription plan',
                $e,
                ['plan_id' => $planId]
            );

            return $this->serverErrorResponse('Failed to delete subscription plan');
        }
    }

    /**
     * Toggle plan active status.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function toggleStatus(Request $request, string $planId): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean',
        ]);

        try {
            $plan = $this->planService->togglePlanStatus($planId, $request->boolean('is_active'));

            return $this->successResponse(
                $plan,
                'Plan status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to toggle plan status',
                $e,
                ['plan_id' => $planId, 'is_active' => $request->boolean('is_active')]
            );

            return $this->serverErrorResponse('Failed to update plan status');
        }
    }

    /**
     * Manage plan pricing.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function managePricing(Request $request, string $planId): JsonResponse
    {
        $request->validate([
            'prices' => 'required|array',
            'prices.*.billing_interval' => 'required|string|in:monthly,quarterly,annually',
            'prices.*.price' => 'required|numeric|min:0',
            'prices.*.currency' => 'required|string|in:NGN,USD',
            'prices.*.is_active' => 'sometimes|boolean',
        ]);

        try {
            $plan = $this->planService->updatePlanPricing($planId, $request->input('prices'));

            return $this->successResponse(
                $plan,
                'Plan pricing updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update plan pricing',
                $e,
                ['plan_id' => $planId, 'prices' => $request->input('prices')]
            );

            return $this->serverErrorResponse('Failed to update plan pricing');
        }
    }

    /**
     * Manage plan features.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function manageFeatures(Request $request, string $planId): JsonResponse
    {
        $request->validate([
            'features' => 'required|array',
            'features.*.feature_id' => 'required|uuid|exists:features,id',
            'features.*.limit' => 'nullable|integer|min:0',
            'features.*.is_enabled' => 'required|boolean',
        ]);

        try {
            $plan = $this->planService->updatePlanFeatures($planId, $request->input('features'));

            return $this->successResponse(
                $plan,
                'Plan features updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update plan features',
                $e,
                ['plan_id' => $planId, 'features' => $request->input('features')]
            );

            return $this->serverErrorResponse('Failed to update plan features');
        }
    }

    /**
     * Get plan analytics.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function analytics(Request $request, string $planId): JsonResponse
    {
        try {
            $analytics = $this->planService->getPlanAnalytics($planId);

            return $this->successResponse(
                $analytics,
                'Plan analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve plan analytics',
                $e,
                ['plan_id' => $planId]
            );

            return $this->serverErrorResponse('Failed to retrieve plan analytics');
        }
    }

    /**
     * Clone existing plan.
     *
     * @group Admin Plans
     *
     * @authenticated
     */
    public function clone(Request $request, string $planId): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:subscription_plans,slug',
        ]);

        try {
            $newPlan = $this->planService->clonePlan($planId, $request->only(['name', 'slug']));

            return $this->createdResponse(
                $newPlan,
                'Plan cloned successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to clone plan',
                $e,
                ['plan_id' => $planId, 'request_data' => $request->only(['name', 'slug'])]
            );

            return $this->serverErrorResponse('Failed to clone plan');
        }
    }
}
