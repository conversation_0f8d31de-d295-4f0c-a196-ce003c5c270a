<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Services\System\MacroManagementService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Macro Controller
 *
 * Handles comprehensive macro and automation management for administrators.
 * Manages macro creation, execution, scheduling, and performance monitoring.
 */
class MacroController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly MacroManagementService $macroService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all macros with filtering.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'sometimes|string|in:draft,published,archived',
                'category' => 'sometimes|string|in:data_processing,notifications,reporting,maintenance',
                'trigger_type' => 'sometimes|string|in:time_based,event_based,manual,api_call',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'is_active' => 'sometimes|boolean',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $macros = $this->macroService->getMacros($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedMacros = array_slice($macros, $offset, $perPage);
            $total = count($macros);

            return $this->successResponse([
                'data' => array_map([$this, 'transformMacro'], $paginatedMacros),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Macros retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve macros',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve macros');
        }
    }

    /**
     * Create a new macro.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'category' => 'required|string|in:data_processing,notifications,reporting,maintenance',
                'trigger_type' => 'required|string|in:time_based,event_based,manual,api_call',
                'trigger_config' => 'required|array',
                'actions' => 'required|array|min:1',
                'actions.*.type' => 'required|string',
                'actions.*.config' => 'sometimes|array',
                'conditions' => 'sometimes|array',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'timeout_seconds' => 'sometimes|integer|min:30|max:3600',
                'retry_attempts' => 'sometimes|integer|min:0|max:10',
                'metadata' => 'sometimes|array',
            ]);

            $macro = $this->macroService->createMacro($validated);

            return $this->successResponse(
                $this->transformMacro($macro),
                'Macro created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create macro',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create macro');
        }
    }

    /**
     * Get macro analytics and performance metrics.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'category' => 'sometimes|string|in:data_processing,notifications,reporting,maintenance',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->macroService->getMacroAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Macro analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve macro analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve macro analytics');
        }
    }

    /**
     * Get macro templates.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function templates(): JsonResponse
    {
        try {
            $templates = $this->macroService->getMacroTemplates();

            return $this->successResponse(
                $templates,
                'Macro templates retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve macro templates',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve macro templates');
        }
    }

    /**
     * Update macro configuration.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function update(Request $request, string $macroId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'actions' => 'sometimes|array|min:1',
                'actions.*.type' => 'sometimes|string',
                'actions.*.config' => 'sometimes|array',
                'conditions' => 'sometimes|array',
                'trigger_config' => 'sometimes|array',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'timeout_seconds' => 'sometimes|integer|min:30|max:3600',
                'retry_attempts' => 'sometimes|integer|min:0|max:10',
                'status' => 'sometimes|string|in:draft,published,archived',
            ]);

            $macro = $this->macroService->updateMacro($macroId, $validated);

            return $this->successResponse(
                $this->transformMacro($macro),
                'Macro updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Macro not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update macro',
                $e,
                ['macro_id' => $macroId, 'updates' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update macro');
        }
    }

    /**
     * Execute macro manually.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function execute(Request $request, string $macroId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'context' => 'sometimes|array',
                'force_execution' => 'sometimes|boolean',
            ]);

            $execution = $this->macroService->executeMacro(
                $macroId,
                $validated['context'] ?? []
            );

            return $this->successResponse(
                $execution,
                'Macro executed successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to execute macro',
                $e,
                ['macro_id' => $macroId, 'context' => $request->get('context', [])]
            );

            return $this->serverErrorResponse('Failed to execute macro');
        }
    }

    /**
     * Toggle macro activation status.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function toggle(Request $request, string $macroId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'is_active' => 'required|boolean',
            ]);

            $macro = $this->macroService->toggleMacro(
                $macroId,
                $validated['is_active']
            );

            $action = $validated['is_active'] ? 'activated' : 'deactivated';

            return $this->successResponse(
                $this->transformMacro($macro),
                "Macro {$action} successfully"
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to toggle macro',
                $e,
                ['macro_id' => $macroId, 'is_active' => $request->get('is_active')]
            );

            return $this->serverErrorResponse('Failed to toggle macro');
        }
    }

    /**
     * Create macro from template.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function createFromTemplate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'template_key' => 'required|string',
                'customizations' => 'sometimes|array',
                'customizations.name' => 'sometimes|string|max:255',
                'customizations.description' => 'sometimes|string|max:1000',
                'customizations.tenant_id' => 'sometimes|uuid|exists:tenants,id',
            ]);

            $macro = $this->macroService->createFromTemplate(
                $validated['template_key'],
                $validated['customizations'] ?? []
            );

            return $this->successResponse(
                $this->transformMacro($macro),
                'Macro created from template successfully',
                201
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create macro from template',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create macro from template');
        }
    }

    /**
     * Delete macro.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function destroy(Request $request, string $macroId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->macroService->deleteMacro(
                $macroId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Macro deleted successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete macro',
                $e,
                ['macro_id' => $macroId]
            );

            return $this->serverErrorResponse('Failed to delete macro');
        }
    }

    /**
     * Bulk macro operations.
     *
     * @group Admin Macros
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'macro_ids' => 'required|array|min:1',
                'macro_ids.*' => 'string',
                'action' => 'required|string|in:activate,deactivate,publish,delete',
                'reason' => 'sometimes|string|max:500',
            ]);

            $results = $this->macroService->bulkMacroOperations(
                $validated['macro_ids'],
                $validated['action'],
                array_filter([
                    'reason' => $validated['reason'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk macro operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk macro operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform macro for API response.
     */
    public function transformMacro(array $macro): array
    {
        return [
            'id' => $macro['id'],
            'name' => $macro['name'],
            'description' => $macro['description'],
            'category' => $macro['category'],
            'trigger_type' => $macro['trigger_type'],
            'trigger_config' => $macro['trigger_config'],
            'actions' => $macro['actions'],
            'conditions' => $macro['conditions'],
            'tenant_id' => $macro['tenant_id'],
            'status' => $macro['status'],
            'is_active' => $macro['is_active'],
            'priority' => $macro['priority'],
            'timeout_seconds' => $macro['timeout_seconds'],
            'retry_attempts' => $macro['retry_attempts'],
            'created_at' => $macro['created_at'],
            'created_by' => $macro['created_by'],
            'updated_at' => $macro['updated_at'],
            'version' => $macro['version'],
            'execution_count' => $macro['execution_count'],
            'success_count' => $macro['success_count'],
            'failure_count' => $macro['failure_count'],
            'last_executed' => $macro['last_executed'],
            'average_duration' => $macro['average_duration'],
            'success_rate' => $macro['execution_count'] > 0
                ? round(($macro['success_count'] / $macro['execution_count']) * 100, 2)
                : 0,
            'metadata' => $macro['metadata'],
        ];
    }
}
