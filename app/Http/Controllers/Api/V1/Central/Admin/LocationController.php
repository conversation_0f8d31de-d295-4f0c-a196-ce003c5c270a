<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Core\City;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Services\Core\LocationManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Location Controller
 *
 * Handles geographic data management for administrators.
 * Manages countries, states, cities, and location-based configurations.
 */
class LocationController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly LocationManagementService $locationService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get location hierarchy.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function index(): JsonResponse
    {
        try {
            $hierarchy = $this->locationService->getLocationHierarchy();

            return $this->successResponse(
                $hierarchy,
                'Location hierarchy retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve location hierarchy',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve location hierarchy');
        }
    }

    /**
     * Get all countries.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function countries(Request $request): JsonResponse
    {
        try {
            $query = Country::query()->withCount(['states', 'cities', 'addresses']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'code',
                    'currency_code',
                    'phone_code',
                ],
                'sortFields' => [
                    'name',
                    'code',
                    'currency_code',
                    'phone_code',
                    'created_at',
                    'is_active',
                ],
                'filters' => [
                    'is_active' => ['type' => 'boolean'],
                    'currency_code' => ['type' => 'exact'],
                ],
                'message' => 'Countries retrieved successfully',
                'entityName' => 'countries',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve countries',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve countries');
        }
    }

    /**
     * Create new country.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function storeCountry(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'required|string|size:2|unique:countries,code',
                'currency_code' => 'required|string|size:3',
                'phone_code' => 'required|string|max:10',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'is_active' => 'nullable|boolean',
            ]);

            $country = $this->locationService->createCountry($validated);

            return $this->successResponse(
                $country,
                'Country created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create country',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create country');
        }
    }

    /**
     * Show specific country.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function showCountry(string $id): JsonResponse
    {
        try {
            $countryDetails = $this->locationService->getCountryWithDetails($id);

            return $this->successResponse(
                $countryDetails,
                'Country details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Country not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve country details',
                $e,
                ['country_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve country details');
        }
    }

    /**
     * Update country.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function updateCountry(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'code' => 'sometimes|required|string|size:2|unique:countries,code,'.$id,
                'currency_code' => 'sometimes|required|string|size:3',
                'phone_code' => 'sometimes|required|string|max:10',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'is_active' => 'nullable|boolean',
            ]);

            $country = $this->locationService->updateCountry($id, $validated);

            return $this->successResponse(
                $country,
                'Country updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Country not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update country',
                $e,
                ['country_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update country');
        }
    }

    /**
     * Get all states.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function states(Request $request): JsonResponse
    {
        try {
            $query = State::query()
                ->with(['country:id,name,code'])
                ->withCount(['cities', 'addresses']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'code',
                ],
                'sortFields' => [
                    'name',
                    'code',
                    'created_at',
                    'is_active',
                ],
                'filters' => [
                    'country_id' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'States retrieved successfully',
                'entityName' => 'states',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve states',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve states');
        }
    }

    /**
     * Create new state.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function storeState(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'country_id' => 'required|uuid|exists:countries,id',
                'name' => 'required|string|max:255',
                'code' => 'required|string|max:10',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'is_active' => 'nullable|boolean',
            ]);

            $state = $this->locationService->createState($validated);

            return $this->successResponse(
                $state->load('country:id,name,code'),
                'State created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create state',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create state');
        }
    }

    /**
     * Update state.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function updateState(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'country_id' => 'sometimes|required|uuid|exists:countries,id',
                'name' => 'sometimes|required|string|max:255',
                'code' => 'sometimes|required|string|max:10',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'is_active' => 'nullable|boolean',
            ]);

            $state = $this->locationService->updateState($id, $validated);

            return $this->successResponse(
                $state->load('country:id,name,code'),
                'State updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('State not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update state',
                $e,
                ['state_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update state');
        }
    }

    /**
     * Get all cities.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function cities(Request $request): JsonResponse
    {
        try {
            $query = City::query()
                ->with(['country:id,name,code', 'state:id,name,code'])
                ->withCount('addresses');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                ],
                'sortFields' => [
                    'name',
                    'created_at',
                    'is_active',
                ],
                'filters' => [
                    'country_id' => ['type' => 'exact'],
                    'state_id' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Cities retrieved successfully',
                'entityName' => 'cities',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve cities',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve cities');
        }
    }

    /**
     * Create new city.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function storeCity(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'country_id' => 'required|uuid|exists:countries,id',
                'state_id' => 'nullable|uuid|exists:states,id',
                'name' => 'required|string|max:255',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'is_active' => 'nullable|boolean',
            ]);

            $city = $this->locationService->createCity($validated);

            return $this->successResponse(
                $city->load(['country:id,name,code', 'state:id,name,code']),
                'City created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create city',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create city');
        }
    }

    /**
     * Update city.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function updateCity(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'country_id' => 'sometimes|required|uuid|exists:countries,id',
                'state_id' => 'nullable|uuid|exists:states,id',
                'name' => 'sometimes|required|string|max:255',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'is_active' => 'nullable|boolean',
            ]);

            $city = $this->locationService->updateCity($id, $validated);

            return $this->successResponse(
                $city->load(['country:id,name,code', 'state:id,name,code']),
                'City updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('City not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update city',
                $e,
                ['city_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update city');
        }
    }

    /**
     * Search locations.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'query' => 'required|string|min:2|max:100',
                'type' => 'nullable|string|in:countries,states,cities',
            ]);

            $results = $this->locationService->searchLocations(
                $validated['query'],
                $validated['type'] ?? null
            );

            return $this->successResponse(
                $results,
                'Location search completed successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to search locations',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to search locations');
        }
    }

    /**
     * Bulk import locations.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function bulkImport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|in:countries,states,cities',
                'locations' => 'required|array|min:1|max:1000',
                'locations.*' => 'required|array',
            ]);

            $results = $this->locationService->bulkImportLocations(
                $validated['locations'],
                $validated['type']
            );

            return $this->successResponse(
                $results,
                "Bulk import completed: {$results['success']} successful, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk import locations',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk import locations');
        }
    }

    /**
     * Get location statistics.
     *
     * @group Admin Locations
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->locationService->getLocationStatistics();

            return $this->successResponse(
                $stats,
                'Location statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve location statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve location statistics');
        }
    }
}
