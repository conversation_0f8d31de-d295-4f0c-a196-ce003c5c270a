<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\BackupManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Backup Controller
 *
 * Handles comprehensive backup and recovery management for administrators.
 * Manages automated backups, verification, and restoration processes.
 */
class BackupController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly BackupManagementService $backupService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all backups with filtering.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:full,incremental,differential',
                'status' => 'sometimes|string|in:completed,failed,in_progress',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $backups = $this->backupService->getBackups($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedBackups = array_slice($backups, $offset, $perPage);
            $total = count($backups);

            return $this->successResponse([
                'data' => array_map([$this, 'transformBackup'], $paginatedBackups),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Backups retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve backups',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve backups');
        }
    }

    /**
     * Create a new backup.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|in:full,incremental,differential',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'description' => 'sometimes|string|max:500',
                'metadata' => 'sometimes|array',
            ]);

            $backup = $this->backupService->createBackup($validated);

            return $this->successResponse(
                $this->transformBackup($backup),
                'Backup created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create backup',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create backup');
        }
    }

    /**
     * Get backup analytics and insights.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->backupService->getBackupAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Backup analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve backup analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve backup analytics');
        }
    }

    /**
     * Verify backup integrity.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function verify(string $backupId): JsonResponse
    {
        try {
            $verification = $this->backupService->verifyBackup($backupId);

            return $this->successResponse(
                $verification,
                'Backup verification completed'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Backup not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify backup',
                $e,
                ['backup_id' => $backupId]
            );

            return $this->serverErrorResponse('Failed to verify backup');
        }
    }

    /**
     * Restore from backup.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function restore(Request $request, string $backupId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:full,partial',
                'target_tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'options' => 'sometimes|array',
            ]);

            $restoration = $this->backupService->restoreFromBackup($backupId, $validated);

            return $this->successResponse(
                $restoration,
                'Backup restoration started successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to restore backup',
                $e,
                ['backup_id' => $backupId, 'options' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to restore backup');
        }
    }

    /**
     * Schedule automated backup.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function schedule(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'type' => 'required|string|in:full,incremental,differential',
                'frequency' => 'required|string|in:daily,weekly,monthly',
                'time' => 'sometimes|string|regex:/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'retention_days' => 'sometimes|integer|min:1|max:365',
                'is_active' => 'sometimes|boolean',
            ]);

            $schedule = $this->backupService->scheduleBackup($validated);

            return $this->successResponse(
                $schedule,
                'Backup scheduled successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to schedule backup',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to schedule backup');
        }
    }

    /**
     * Delete backup.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function destroy(Request $request, string $backupId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->backupService->deleteBackup(
                $backupId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Backup deleted successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Backup not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete backup',
                $e,
                ['backup_id' => $backupId]
            );

            return $this->serverErrorResponse('Failed to delete backup');
        }
    }

    /**
     * Bulk backup operations.
     *
     * @group Admin Backups
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'backup_ids' => 'required|array|min:1',
                'backup_ids.*' => 'string',
                'action' => 'required|string|in:verify,delete',
                'reason' => 'sometimes|string|max:500',
            ]);

            $results = $this->backupService->bulkBackupOperations(
                $validated['backup_ids'],
                $validated['action'],
                array_filter([
                    'reason' => $validated['reason'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk backup operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk backup operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform backup for API response.
     */
    public function transformBackup(array $backup): array
    {
        return [
            'id' => $backup['id'],
            'type' => $backup['type'],
            'tenant_id' => $backup['tenant_id'],
            'description' => $backup['description'],
            'status' => $backup['status'],
            'size' => $backup['size'],
            'size_formatted' => $this->formatBytes($backup['size']),
            'file_path' => $backup['file_path'],
            'created_at' => $backup['created_at'],
            'completed_at' => $backup['completed_at'],
            'verified_at' => $backup['verified_at'],
            'is_verified' => $backup['verified_at'] !== null,
            'metadata' => $backup['metadata'],
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2).' '.$units[$pow];
    }
}
