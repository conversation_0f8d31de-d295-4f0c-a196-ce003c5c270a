<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\System\ConfigurationType;
use App\Http\Controllers\Controller;
use App\Models\System\ConfigurationSetting;
use App\Services\System\ConfigurationService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * Admin Configuration Controller
 *
 * Handles platform configuration management for administrators.
 * Manages global platform settings, feature flags, API configurations, and system parameters.
 */
class ConfigController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly ConfigurationService $configService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all configuration groups.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function index(): JsonResponse
    {
        try {
            $groups = $this->configService->getAllGroups();

            return $this->successResponse(
                $groups,
                'Configuration groups retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration groups',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve configuration groups');
        }
    }

    /**
     * Get configuration group with details.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function showGroup(string $groupId): JsonResponse
    {
        try {
            $groupDetails = $this->configService->getGroupWithDetails($groupId);

            return $this->successResponse(
                $groupDetails,
                'Configuration group details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Configuration group not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration group details',
                $e,
                ['group_id' => $groupId]
            );

            return $this->serverErrorResponse('Failed to retrieve configuration group details');
        }
    }

    /**
     * Create new configuration group.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function storeGroup(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:configuration_groups,slug',
                'description' => 'nullable|string|max:500',
                'icon' => 'nullable|string|max:100',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean',
            ]);

            $group = $this->configService->createGroup($validated);

            return $this->successResponse(
                $group,
                'Configuration group created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create configuration group',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create configuration group');
        }
    }

    /**
     * Update configuration group.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function updateGroup(Request $request, string $groupId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'slug' => 'sometimes|string|max:255|unique:configuration_groups,slug,'.$groupId,
                'description' => 'nullable|string|max:500',
                'icon' => 'nullable|string|max:100',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean',
            ]);

            $group = $this->configService->updateGroup($groupId, $validated);

            return $this->successResponse(
                $group,
                'Configuration group updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Configuration group not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update configuration group',
                $e,
                ['group_id' => $groupId, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update configuration group');
        }
    }

    /**
     * Get all configuration settings.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function settings(Request $request): JsonResponse
    {
        try {
            $query = ConfigurationSetting::query()
                ->with([
                    'group:id,name,slug',
                    'updatedBy:id,first_name,last_name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'key',
                    'name',
                    'description',
                ],
                'sortFields' => [
                    'key',
                    'name',
                    'type',
                    'updated_at',
                    'is_required',
                    'is_active',
                ],
                'filters' => [
                    'group_id' => ['type' => 'exact'],
                    'type' => ['type' => 'exact'],
                    'is_required' => ['type' => 'boolean'],
                    'is_active' => ['type' => 'boolean'],
                    'is_sensitive' => ['type' => 'boolean'],
                ],
                'message' => 'Configuration settings retrieved successfully',
                'entityName' => 'settings',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration settings',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve configuration settings');
        }
    }

    /**
     * Create new configuration setting.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function storeSetting(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'group_id' => 'nullable|uuid|exists:configuration_groups,id',
                'key' => 'required|string|max:255|unique:configuration_settings,key',
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'type' => ['required', Rule::enum(ConfigurationType::class)],
                'value' => 'nullable',
                'default_value' => 'nullable',
                'validation_rules' => 'nullable|array',
                'options' => 'nullable|array',
                'is_required' => 'nullable|boolean',
                'is_active' => 'nullable|boolean',
                'is_sensitive' => 'nullable|boolean',
            ]);

            $setting = $this->configService->createSetting($validated);

            return $this->successResponse(
                $setting->load(['group:id,name,slug']),
                'Configuration setting created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create configuration setting',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create configuration setting');
        }
    }

    /**
     * Update configuration setting.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function updateSetting(Request $request, string $settingId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'group_id' => 'nullable|uuid|exists:configuration_groups,id',
                'name' => 'sometimes|required|string|max:255',
                'description' => 'nullable|string|max:500',
                'value' => 'nullable',
                'default_value' => 'nullable',
                'validation_rules' => 'nullable|array',
                'options' => 'nullable|array',
                'is_required' => 'nullable|boolean',
                'is_active' => 'nullable|boolean',
                'is_sensitive' => 'nullable|boolean',
                'change_reason' => 'nullable|string|max:255',
            ]);

            $setting = $this->configService->updateSetting($settingId, $validated);

            return $this->successResponse(
                $setting->load(['group:id,name,slug']),
                'Configuration setting updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Configuration setting not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\InvalidArgumentException $e) {
            return $this->badRequestResponse($e->getMessage());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update configuration setting',
                $e,
                ['setting_id' => $settingId, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update configuration setting');
        }
    }

    /**
     * Get configuration value by key.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function getValue(string $key): JsonResponse
    {
        try {
            $value = $this->configService->getValue($key);

            return $this->successResponse(
                ['key' => $key, 'value' => $value],
                'Configuration value retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration value',
                $e,
                ['key' => $key]
            );

            return $this->serverErrorResponse('Failed to retrieve configuration value');
        }
    }

    /**
     * Set configuration value by key.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function setValue(Request $request, string $key): JsonResponse
    {
        try {
            $validated = $request->validate([
                'value' => 'required',
                'reason' => 'nullable|string|max:255',
            ]);

            $this->configService->setValue($key, $validated['value'], $validated['reason'] ?? null);

            return $this->successResponse(
                ['key' => $key, 'value' => $validated['value']],
                'Configuration value updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->badRequestResponse($e->getMessage());
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to set configuration value',
                $e,
                ['key' => $key, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to set configuration value');
        }
    }

    /**
     * Bulk update configuration values.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'settings' => 'required|array',
                'settings.*' => 'required',
                'reason' => 'nullable|string|max:255',
            ]);

            $results = $this->configService->bulkUpdate(
                $validated['settings'],
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                $results,
                'Configuration settings updated successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update configuration settings',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk update configuration settings');
        }
    }

    /**
     * Get configuration statistics.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->configService->getStatistics();

            return $this->successResponse(
                $stats,
                'Configuration statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve configuration statistics');
        }
    }

    /**
     * Get available configuration types.
     *
     * @group Admin Configuration
     *
     * @authenticated
     */
    public function types(): JsonResponse
    {
        try {
            $types = collect(ConfigurationType::cases())->map(function ($type) {
                return [
                    'value' => $type->value,
                    'label' => $type->label(),
                    'description' => $type->description(),
                    'html_input_type' => $type->getHtmlInputType(),
                    'requires_options' => $type->requiresOptions(),
                    'is_sensitive' => $type->isSensitive(),
                    'is_multiple' => $type->isMultiple(),
                    'default_value' => $type->getDefaultValue(),
                    'validation_rules' => $type->getValidationRules(),
                ];
            });

            return $this->successResponse(
                $types,
                'Configuration types retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration types',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve configuration types');
        }
    }
}
