<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Business\ProductCollectionType;
use App\Http\Controllers\Controller;
use App\Models\Business\ProductCollection;
use App\Services\Business\CollectionManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Collection Controller
 *
 * Handles product collection management for administrators.
 * Manages cross-tenant collections, templates, analytics, and bulk operations.
 */
class CollectionController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly CollectionManagementService $collectionService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all product collections.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = ProductCollection::query()
                ->with([
                    'business:id,name,slug',
                    'businessBranch:id,name',
                ])
                ->withCount(['categories', 'products']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                    'slug',
                ],
                'sortFields' => [
                    'name',
                    'type',
                    'is_active',
                    'display_order',
                    'created_at',
                ],
                'filters' => [
                    'business_id' => ['type' => 'exact'],
                    'business_branch_id' => ['type' => 'exact'],
                    'type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'has_products' => ['type' => 'custom', 'callback' => function ($query, $value) {
                        if ($value) {
                            $query->has('products');
                        } else {
                            $query->doesntHave('products');
                        }
                    }],
                ],
                'message' => 'Product collections retrieved successfully',
                'entityName' => 'collections',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product collections',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve product collections');
        }
    }

    /**
     * Show specific collection.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $collection = ProductCollection::with([
                'business:id,name,slug',
                'businessBranch:id,name',
                'categories:id,product_collection_id,name,display_order',
                'products:id,name,price,main_image_url',
            ])->findOrFail($id);

            $performance = $this->collectionService->getCollectionPerformance($id);

            return $this->successResponse(
                [
                    'collection' => $collection,
                    'performance' => $performance,
                ],
                'Collection details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Collection not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve collection details',
                $e,
                ['collection_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve collection details');
        }
    }

    /**
     * Create collection template.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function createTemplate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'tenant_id' => 'required|string|exists:tenants,id',
                'business_id' => 'required|uuid|exists:businesses,id',
                'business_branch_id' => 'nullable|uuid|exists:business_branches,id',
                'name' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
                'slug' => 'required|string|max:255',
                'type' => 'required|string|in:'.implode(',', array_column(ProductCollectionType::cases(), 'value')),
                'is_active' => 'nullable|boolean',
                'active_start_time' => 'nullable|date',
                'active_end_time' => 'nullable|date|after:active_start_time',
                'display_order' => 'nullable|integer|min:0',
                'image_url' => 'nullable|url',
            ]);

            $collection = $this->collectionService->createCollectionTemplate($validated);

            return $this->successResponse(
                $collection,
                'Collection template created successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create collection template',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create collection template');
        }
    }

    /**
     * Bulk update collection status.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'collection_ids' => 'required|array|min:1|max:50',
                'collection_ids.*' => 'uuid|exists:product_collections,id',
                'is_active' => 'required|boolean',
            ]);

            $results = $this->collectionService->bulkUpdateCollectionStatus(
                $validated['collection_ids'],
                $validated['is_active']
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update collection status',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk update collection status');
        }
    }

    /**
     * Get collection statistics.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->collectionService->getCollectionStatistics();

            return $this->successResponse(
                $stats,
                'Collection statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve collection statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve collection statistics');
        }
    }

    /**
     * Search collections.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'query' => 'required|string|min:2|max:100',
                'type' => 'nullable|string|in:'.implode(',', array_column(ProductCollectionType::cases(), 'value')),
            ]);

            $results = $this->collectionService->searchCollections(
                $validated['query'],
                $validated['type'] ?? null
            );

            return $this->successResponse(
                $results,
                'Collection search completed successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to search collections',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to search collections');
        }
    }

    /**
     * Get collections by business.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function byBusiness(string $businessId): JsonResponse
    {
        try {
            $collections = $this->collectionService->getCollectionsByBusiness($businessId);

            return $this->successResponse(
                $collections,
                'Business collections retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve business collections',
                $e,
                ['business_id' => $businessId]
            );

            return $this->serverErrorResponse('Failed to retrieve business collections');
        }
    }

    /**
     * Get collections by type.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function byType(string $type): JsonResponse
    {
        try {
            $collectionType = ProductCollectionType::from($type);
            $collections = $this->collectionService->getCollectionsByType($collectionType);

            return $this->successResponse(
                $collections,
                "Collections of type '{$type}' retrieved successfully"
            );

        } catch (\ValueError $e) {
            return $this->errorResponse('Invalid collection type', 400);
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve collections by type',
                $e,
                ['type' => $type]
            );

            return $this->serverErrorResponse('Failed to retrieve collections by type');
        }
    }

    /**
     * Clone collection.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function clone(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'target_business_id' => 'required|uuid|exists:businesses,id',
                'target_tenant_id' => 'required|string|exists:tenants,id',
            ]);

            $newCollection = $this->collectionService->cloneCollection(
                $id,
                $validated['target_business_id'],
                $validated['target_tenant_id']
            );

            return $this->successResponse(
                $newCollection,
                'Collection cloned successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Collection not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to clone collection',
                $e,
                ['collection_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to clone collection');
        }
    }

    /**
     * Get featured collections.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function featured(): JsonResponse
    {
        try {
            $collections = $this->collectionService->getFeaturedCollections();

            return $this->successResponse(
                $collections,
                'Featured collections retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve featured collections',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve featured collections');
        }
    }

    /**
     * Get seasonal collections.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function seasonal(): JsonResponse
    {
        try {
            $collections = $this->collectionService->getSeasonalCollections();

            return $this->successResponse(
                $collections,
                'Seasonal collections retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve seasonal collections',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve seasonal collections');
        }
    }

    /**
     * Update collection scheduling.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function updateScheduling(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'active_start_time' => 'nullable|date',
                'active_end_time' => 'nullable|date|after:active_start_time',
                'is_active' => 'nullable|boolean',
            ]);

            $collection = $this->collectionService->updateCollectionScheduling($id, $validated);

            return $this->successResponse(
                $collection,
                'Collection scheduling updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Collection not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update collection scheduling',
                $e,
                ['collection_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update collection scheduling');
        }
    }

    /**
     * Get collection templates.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function templates(): JsonResponse
    {
        try {
            $templates = $this->collectionService->getCollectionTemplates();

            return $this->successResponse(
                $templates,
                'Collection templates retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve collection templates',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve collection templates');
        }
    }

    /**
     * Export collections.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'format' => 'required|string|in:csv,json',
                'business_id' => 'nullable|uuid|exists:businesses,id',
                'type' => 'nullable|string|in:'.implode(',', array_column(ProductCollectionType::cases(), 'value')),
                'is_active' => 'nullable|boolean',
            ]);

            $filename = $this->collectionService->exportCollections(
                array_filter($validated, fn ($value) => $value !== null),
                $validated['format']
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.collections.download', ['filename' => $filename]),
                ],
                'Collections exported successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export collections',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to export collections');
        }
    }

    /**
     * Download exported collections.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^collections_export_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download collection export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download collection export');
        }
    }

    /**
     * Get available collection types.
     *
     * @group Admin Collections
     *
     * @authenticated
     */
    public function types(): JsonResponse
    {
        try {
            $types = array_map(fn ($case) => [
                'value' => $case->value,
                'label' => ucfirst(str_replace('_', ' ', $case->value)),
            ], ProductCollectionType::cases());

            return $this->successResponse(
                $types,
                'Collection types retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve collection types',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve collection types');
        }
    }
}
