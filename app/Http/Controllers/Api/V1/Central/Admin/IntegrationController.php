<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\System\IntegrationStatus;
use App\Enums\System\IntegrationType;
use App\Http\Controllers\Controller;
use App\Models\System\Integration;
use App\Models\System\IntegrationLog;
use App\Services\System\IntegrationManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * Admin Integration Controller
 *
 * Handles external service integration management for administrators.
 * Manages payment gateways, SMS providers, email services, and other third-party integrations.
 */
class IntegrationController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly IntegrationManagementService $integrationService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all integrations.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Integration::query()
                ->with([
                    'createdBy:id,first_name,last_name',
                    'updatedBy:id,first_name,last_name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'slug',
                    'description',
                    'provider',
                ],
                'sortFields' => [
                    'name',
                    'type',
                    'status',
                    'provider',
                    'created_at',
                    'last_tested_at',
                    'is_active',
                ],
                'filters' => [
                    'type' => ['type' => 'exact'],
                    'status' => ['type' => 'exact'],
                    'provider' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'is_sandbox' => ['type' => 'boolean'],
                ],
                'message' => 'Integrations retrieved successfully',
                'entityName' => 'integrations',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integrations',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve integrations');
        }
    }

    /**
     * Create new integration.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:integrations,slug',
                'type' => ['required', Rule::enum(IntegrationType::class)],
                'status' => ['nullable', Rule::enum(IntegrationStatus::class)],
                'description' => 'nullable|string|max:500',
                'provider' => 'nullable|string|max:255',
                'version' => 'nullable|string|max:50',
                'configuration' => 'nullable|array',
                'credentials' => 'nullable|array',
                'settings' => 'nullable|array',
                'endpoints' => 'nullable|array',
                'is_active' => 'nullable|boolean',
                'is_sandbox' => 'nullable|boolean',
            ]);

            $integration = $this->integrationService->createIntegration($validated);

            return $this->successResponse(
                $integration->load(['createdBy:id,first_name,last_name']),
                'Integration created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create integration',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create integration');
        }
    }

    /**
     * Show specific integration.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $integrationDetails = $this->integrationService->getIntegrationWithDetails($id);

            return $this->successResponse(
                $integrationDetails,
                'Integration details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Integration not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integration details',
                $e,
                ['integration_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve integration details');
        }
    }

    /**
     * Update integration.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'slug' => 'sometimes|string|max:255|unique:integrations,slug,'.$id,
                'type' => ['sometimes', Rule::enum(IntegrationType::class)],
                'status' => ['sometimes', Rule::enum(IntegrationStatus::class)],
                'description' => 'nullable|string|max:500',
                'provider' => 'nullable|string|max:255',
                'version' => 'nullable|string|max:50',
                'configuration' => 'nullable|array',
                'credentials' => 'nullable|array',
                'settings' => 'nullable|array',
                'endpoints' => 'nullable|array',
                'is_active' => 'nullable|boolean',
                'is_sandbox' => 'nullable|boolean',
            ]);

            $integration = $this->integrationService->updateIntegration($id, $validated);

            return $this->successResponse(
                $integration->load(['updatedBy:id,first_name,last_name']),
                'Integration updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Integration not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update integration',
                $e,
                ['integration_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update integration');
        }
    }

    /**
     * Delete integration.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $this->integrationService->deleteIntegration($id);

            return $this->successResponse(
                null,
                'Integration deleted successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Integration not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete integration',
                $e,
                ['integration_id' => $id]
            );

            return $this->serverErrorResponse('Failed to delete integration');
        }
    }

    /**
     * Test integration connection.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function test(string $id): JsonResponse
    {
        try {
            $testResult = $this->integrationService->testIntegration($id);

            return $this->successResponse(
                $testResult,
                $testResult['success'] ? 'Integration test successful' : 'Integration test failed'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Integration not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to test integration',
                $e,
                ['integration_id' => $id]
            );

            return $this->serverErrorResponse('Failed to test integration');
        }
    }

    /**
     * Get integrations by type.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function byType(string $type): JsonResponse
    {
        try {
            $integrationType = IntegrationType::from($type);
            $integrations = $this->integrationService->getIntegrationsByType($integrationType);

            return $this->successResponse(
                $integrations,
                "Integrations of type '{$integrationType->label()}' retrieved successfully"
            );

        } catch (\ValueError $e) {
            return $this->badRequestResponse('Invalid integration type');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integrations by type',
                $e,
                ['type' => $type]
            );

            return $this->serverErrorResponse('Failed to retrieve integrations by type');
        }
    }

    /**
     * Get integration logs.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function logs(Request $request, string $id): JsonResponse
    {
        try {
            $query = IntegrationLog::where('integration_id', $id)
                ->with('integration:id,name');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'method',
                    'endpoint',
                    'status',
                    'error_message',
                ],
                'sortFields' => [
                    'created_at',
                    'method',
                    'endpoint',
                    'status',
                    'response_code',
                    'duration_ms',
                ],
                'filters' => [
                    'method' => ['type' => 'exact'],
                    'status' => ['type' => 'exact'],
                    'response_code' => ['type' => 'exact'],
                ],
                'message' => 'Integration logs retrieved successfully',
                'entityName' => 'logs',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integration logs',
                $e,
                ['integration_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve integration logs');
        }
    }

    /**
     * Get integration statistics.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->integrationService->getIntegrationStatistics();

            return $this->successResponse(
                $stats,
                'Integration statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integration statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve integration statistics');
        }
    }

    /**
     * Get integration health overview.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function health(): JsonResponse
    {
        try {
            $healthData = $this->integrationService->getHealthOverview();

            return $this->successResponse(
                $healthData,
                'Integration health overview retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integration health overview',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve integration health overview');
        }
    }

    /**
     * Bulk update integration statuses.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'integration_ids' => 'required|array|min:1',
                'integration_ids.*' => 'uuid|exists:integrations,id',
                'status' => ['required', Rule::enum(IntegrationStatus::class)],
            ]);

            $results = $this->integrationService->bulkUpdateStatus(
                $validated['integration_ids'],
                IntegrationStatus::from($validated['status'])
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['success']} successful, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update integration statuses',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk update integration statuses');
        }
    }

    /**
     * Get available integration types.
     *
     * @group Admin Integrations
     *
     * @authenticated
     */
    public function types(): JsonResponse
    {
        try {
            $types = collect(IntegrationType::cases())->map(function ($type) {
                return [
                    'value' => $type->value,
                    'label' => $type->label(),
                    'description' => $type->description(),
                    'common_providers' => $type->getCommonProviders(),
                    'required_fields' => $type->getRequiredFields(),
                    'optional_fields' => $type->getOptionalFields(),
                    'supports_webhooks' => $type->supportsWebhooks(),
                    'requires_sandbox' => $type->requiresSandbox(),
                ];
            });

            return $this->successResponse(
                $types,
                'Integration types retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve integration types',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve integration types');
        }
    }
}
