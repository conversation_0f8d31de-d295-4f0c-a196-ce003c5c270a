<?php

declare(strict_types=1);

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * QoreID Verification Model
 * 
 * Stores comprehensive verification results from QoreID API.
 *
 * @property string $id
 * @property string $user_id
 * @property string $verification_type
 * @property string $qoreid_reference
 * @property string $our_reference
 * @property array<array-key, mixed> $verification_data
 * @property int|null $verification_score
 * @property string|null $risk_level
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $verified_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereOurReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereQoreidReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereRiskLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereVerificationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereVerificationScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereVerificationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|QoreIdVerification whereVerifiedAt($value)
 * @mixin \Eloquent
 */
class QoreIdVerification extends Model
{
    use HasUuids;

    protected $table = 'qoreid_verifications';

    protected $fillable = [
        'user_id',
        'verification_type',
        'qoreid_reference',
        'our_reference',
        'verification_data',
        'verification_score',
        'risk_level',
        'status',
        'verified_at',
    ];

    protected $casts = [
        'verification_data' => 'array',
        'verification_score' => 'integer',
        'verified_at' => 'datetime',
    ];

    /**
     * Get the user that owns the verification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if verification is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if verification is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if verification failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get verification score percentage.
     */
    public function getScorePercentage(): int
    {
        return $this->verification_score ?? 0;
    }

    /**
     * Check if verification is high risk.
     */
    public function isHighRisk(): bool
    {
        return $this->risk_level === 'high';
    }

    /**
     * Check if verification is low risk.
     */
    public function isLowRisk(): bool
    {
        return $this->risk_level === 'low';
    }

    /**
     * Get verification summary.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->verification_type,
            'status' => $this->status,
            'score' => $this->verification_score,
            'risk_level' => $this->risk_level,
            'qoreid_reference' => $this->qoreid_reference,
            'verified_at' => $this->verified_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
        ];
    }
}
