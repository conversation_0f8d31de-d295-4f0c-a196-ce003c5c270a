<?php

declare(strict_types=1);

namespace App\Models\Marketing;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Campaign Audience Model
 * 
 * Represents audience segments for marketing campaigns.
 * Defines targeting criteria and estimated audience size.
 *
 * @property string $id
 * @property string $campaign_id
 * @property string $name
 * @property array $criteria
 * @property int $estimated_size
 * @property int|null $actual_size
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Marketing\Campaign $campaign
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CampaignAudience newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CampaignAudience newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CampaignAudience query()
 * @mixin \Eloquent
 */
class CampaignAudience extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'campaign_id',
        'name',
        'criteria',
        'estimated_size',
        'actual_size',
    ];

    protected $casts = [
        'criteria' => 'array',
        'estimated_size' => 'integer',
        'actual_size' => 'integer',
    ];

    /**
     * Available criteria fields.
     */
    public const CRITERIA_ROLE = 'role';

    public const CRITERIA_LOCATION = 'location';

    public const CRITERIA_REGISTRATION_DATE = 'registration_date';

    public const CRITERIA_LAST_LOGIN = 'last_login';

    public const CRITERIA_ORDER_COUNT = 'order_count';

    public const CRITERIA_TOTAL_SPENT = 'total_spent';

    public const CRITERIA_LAST_ORDER_DATE = 'last_order_date';

    public const CRITERIA_SUBSCRIPTION_STATUS = 'subscription_status';

    public const CRITERIA_BUSINESS_CATEGORY = 'business_category';

    public const CRITERIA_PROVIDER_TYPE = 'provider_type';

    /**
     * Available operators.
     */
    public const OPERATOR_EQUALS = 'equals';

    public const OPERATOR_NOT_EQUALS = 'not_equals';

    public const OPERATOR_GREATER_THAN = 'greater_than';

    public const OPERATOR_LESS_THAN = 'less_than';

    public const OPERATOR_GREATER_THAN_OR_EQUAL = 'greater_than_or_equal';

    public const OPERATOR_LESS_THAN_OR_EQUAL = 'less_than_or_equal';

    public const OPERATOR_CONTAINS = 'contains';

    public const OPERATOR_NOT_CONTAINS = 'not_contains';

    public const OPERATOR_IN = 'in';

    public const OPERATOR_NOT_IN = 'not_in';

    public const OPERATOR_BETWEEN = 'between';

    public const OPERATOR_AFTER = 'after';

    public const OPERATOR_BEFORE = 'before';

    /**
     * Get the campaign that owns the audience.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get criteria by field.
     */
    public function getCriteriaByField(string $field): ?array
    {
        foreach ($this->criteria as $criterion) {
            if ($criterion['field'] === $field) {
                return $criterion;
            }
        }

        return null;
    }

    /**
     * Check if audience has criteria for a specific field.
     */
    public function hasCriteriaForField(string $field): bool
    {
        return $this->getCriteriaByField($field) !== null;
    }

    /**
     * Get role criteria.
     */
    public function getRoleCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_ROLE);
    }

    /**
     * Get location criteria.
     */
    public function getLocationCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_LOCATION);
    }

    /**
     * Get registration date criteria.
     */
    public function getRegistrationDateCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_REGISTRATION_DATE);
    }

    /**
     * Get last login criteria.
     */
    public function getLastLoginCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_LAST_LOGIN);
    }

    /**
     * Get order count criteria.
     */
    public function getOrderCountCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_ORDER_COUNT);
    }

    /**
     * Get total spent criteria.
     */
    public function getTotalSpentCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_TOTAL_SPENT);
    }

    /**
     * Get last order date criteria.
     */
    public function getLastOrderDateCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_LAST_ORDER_DATE);
    }

    /**
     * Get subscription status criteria.
     */
    public function getSubscriptionStatusCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_SUBSCRIPTION_STATUS);
    }

    /**
     * Get business category criteria.
     */
    public function getBusinessCategoryCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_BUSINESS_CATEGORY);
    }

    /**
     * Get provider type criteria.
     */
    public function getProviderTypeCriteria(): ?array
    {
        return $this->getCriteriaByField(self::CRITERIA_PROVIDER_TYPE);
    }

    /**
     * Get criteria summary for display.
     */
    public function getCriteriaSummary(): string
    {
        $summaryParts = [];

        foreach ($this->criteria as $criterion) {
            $field = $criterion['field'];
            $operator = $criterion['operator'];
            $value = $criterion['value'];

            $fieldName = $this->getFieldDisplayName($field);
            $operatorName = $this->getOperatorDisplayName($operator);
            $valueName = $this->getValueDisplayName($field, $value);

            $summaryParts[] = "{$fieldName} {$operatorName} {$valueName}";
        }

        return implode(' AND ', $summaryParts);
    }

    /**
     * Get field display name.
     */
    private function getFieldDisplayName(string $field): string
    {
        return match ($field) {
            self::CRITERIA_ROLE => 'Role',
            self::CRITERIA_LOCATION => 'Location',
            self::CRITERIA_REGISTRATION_DATE => 'Registration Date',
            self::CRITERIA_LAST_LOGIN => 'Last Login',
            self::CRITERIA_ORDER_COUNT => 'Order Count',
            self::CRITERIA_TOTAL_SPENT => 'Total Spent',
            self::CRITERIA_LAST_ORDER_DATE => 'Last Order Date',
            self::CRITERIA_SUBSCRIPTION_STATUS => 'Subscription Status',
            self::CRITERIA_BUSINESS_CATEGORY => 'Business Category',
            self::CRITERIA_PROVIDER_TYPE => 'Provider Type',
            default => ucfirst(str_replace('_', ' ', $field)),
        };
    }

    /**
     * Get operator display name.
     */
    private function getOperatorDisplayName(string $operator): string
    {
        return match ($operator) {
            self::OPERATOR_EQUALS => 'is',
            self::OPERATOR_NOT_EQUALS => 'is not',
            self::OPERATOR_GREATER_THAN => 'is greater than',
            self::OPERATOR_LESS_THAN => 'is less than',
            self::OPERATOR_GREATER_THAN_OR_EQUAL => 'is greater than or equal to',
            self::OPERATOR_LESS_THAN_OR_EQUAL => 'is less than or equal to',
            self::OPERATOR_CONTAINS => 'contains',
            self::OPERATOR_NOT_CONTAINS => 'does not contain',
            self::OPERATOR_IN => 'is in',
            self::OPERATOR_NOT_IN => 'is not in',
            self::OPERATOR_BETWEEN => 'is between',
            self::OPERATOR_AFTER => 'is after',
            self::OPERATOR_BEFORE => 'is before',
            default => $operator,
        };
    }

    /**
     * Get value display name.
     */
    private function getValueDisplayName(string $field, $value): string
    {
        if (is_array($value)) {
            return implode(', ', $value);
        }

        if (in_array($field, [self::CRITERIA_REGISTRATION_DATE, self::CRITERIA_LAST_LOGIN, self::CRITERIA_LAST_ORDER_DATE])) {
            return \Illuminate\Support\Carbon::parse($value)->format('M j, Y');
        }

        if (in_array($field, [self::CRITERIA_TOTAL_SPENT])) {
            return '₦'.number_format($value, 2);
        }

        return (string) $value;
    }

    /**
     * Get available criteria fields.
     */
    public static function getAvailableCriteriaFields(): array
    {
        return [
            self::CRITERIA_ROLE => 'User Role',
            self::CRITERIA_LOCATION => 'Location (State)',
            self::CRITERIA_REGISTRATION_DATE => 'Registration Date',
            self::CRITERIA_LAST_LOGIN => 'Last Login Date',
            self::CRITERIA_ORDER_COUNT => 'Number of Orders',
            self::CRITERIA_TOTAL_SPENT => 'Total Amount Spent',
            self::CRITERIA_LAST_ORDER_DATE => 'Last Order Date',
            self::CRITERIA_SUBSCRIPTION_STATUS => 'Subscription Status',
            self::CRITERIA_BUSINESS_CATEGORY => 'Business Category',
            self::CRITERIA_PROVIDER_TYPE => 'Provider Type',
        ];
    }

    /**
     * Get available operators.
     */
    public static function getAvailableOperators(): array
    {
        return [
            self::OPERATOR_EQUALS => 'Equals',
            self::OPERATOR_NOT_EQUALS => 'Not Equals',
            self::OPERATOR_GREATER_THAN => 'Greater Than',
            self::OPERATOR_LESS_THAN => 'Less Than',
            self::OPERATOR_GREATER_THAN_OR_EQUAL => 'Greater Than or Equal',
            self::OPERATOR_LESS_THAN_OR_EQUAL => 'Less Than or Equal',
            self::OPERATOR_CONTAINS => 'Contains',
            self::OPERATOR_NOT_CONTAINS => 'Does Not Contain',
            self::OPERATOR_IN => 'In List',
            self::OPERATOR_NOT_IN => 'Not In List',
            self::OPERATOR_BETWEEN => 'Between',
            self::OPERATOR_AFTER => 'After Date',
            self::OPERATOR_BEFORE => 'Before Date',
        ];
    }

    /**
     * Get operators for a specific field.
     */
    public static function getOperatorsForField(string $field): array
    {
        return match ($field) {
            self::CRITERIA_ROLE, self::CRITERIA_LOCATION, self::CRITERIA_SUBSCRIPTION_STATUS,
            self::CRITERIA_BUSINESS_CATEGORY, self::CRITERIA_PROVIDER_TYPE => [
                self::OPERATOR_EQUALS,
                self::OPERATOR_NOT_EQUALS,
                self::OPERATOR_IN,
                self::OPERATOR_NOT_IN,
            ],
            self::CRITERIA_ORDER_COUNT, self::CRITERIA_TOTAL_SPENT => [
                self::OPERATOR_EQUALS,
                self::OPERATOR_NOT_EQUALS,
                self::OPERATOR_GREATER_THAN,
                self::OPERATOR_LESS_THAN,
                self::OPERATOR_GREATER_THAN_OR_EQUAL,
                self::OPERATOR_LESS_THAN_OR_EQUAL,
                self::OPERATOR_BETWEEN,
            ],
            self::CRITERIA_REGISTRATION_DATE, self::CRITERIA_LAST_LOGIN, self::CRITERIA_LAST_ORDER_DATE => [
                self::OPERATOR_EQUALS,
                self::OPERATOR_NOT_EQUALS,
                self::OPERATOR_AFTER,
                self::OPERATOR_BEFORE,
                self::OPERATOR_BETWEEN,
            ],
            default => [
                self::OPERATOR_EQUALS,
                self::OPERATOR_NOT_EQUALS,
                self::OPERATOR_CONTAINS,
                self::OPERATOR_NOT_CONTAINS,
            ],
        };
    }

    /**
     * Validate criteria structure.
     */
    public function validateCriteria(): bool
    {
        if (! is_array($this->criteria)) {
            return false;
        }

        foreach ($this->criteria as $criterion) {
            if (! isset($criterion['field'], $criterion['operator'], $criterion['value'])) {
                return false;
            }

            $field = $criterion['field'];
            $operator = $criterion['operator'];

            // Check if field is valid
            if (! in_array($field, array_keys(self::getAvailableCriteriaFields()))) {
                return false;
            }

            // Check if operator is valid for the field
            if (! in_array($operator, self::getOperatorsForField($field))) {
                return false;
            }
        }

        return true;
    }
}
