<?php

declare(strict_types=1);

namespace App\Models\Marketing;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Campaign Metric Model
 * 
 * Tracks performance metrics for marketing campaigns.
 * Includes delivery, engagement, and conversion metrics.
 *
 * @property string $id
 * @property string $campaign_id
 * @property int $total_sent
 * @property int $total_delivered
 * @property int $total_bounced
 * @property int $total_opened
 * @property int $total_clicked
 * @property int $total_conversions
 * @property int $total_unsubscribed
 * @property float $revenue_generated
 * @property float $cost_spent
 * @property array|null $channel_breakdown
 * @property array|null $hourly_breakdown
 * @property array|null $daily_breakdown
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Marketing\Campaign $campaign
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CampaignMetric newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CampaignMetric newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CampaignMetric query()
 * @mixin \Eloquent
 */
class CampaignMetric extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'campaign_id',
        'total_sent',
        'total_delivered',
        'total_bounced',
        'total_opened',
        'total_clicked',
        'total_conversions',
        'total_unsubscribed',
        'revenue_generated',
        'cost_spent',
        'channel_breakdown',
        'hourly_breakdown',
        'daily_breakdown',
    ];

    protected $casts = [
        'total_sent' => 'integer',
        'total_delivered' => 'integer',
        'total_bounced' => 'integer',
        'total_opened' => 'integer',
        'total_clicked' => 'integer',
        'total_conversions' => 'integer',
        'total_unsubscribed' => 'integer',
        'revenue_generated' => 'decimal:2',
        'cost_spent' => 'decimal:2',
        'channel_breakdown' => 'array',
        'hourly_breakdown' => 'array',
        'daily_breakdown' => 'array',
    ];

    /**
     * Get the campaign that owns the metrics.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Calculate delivery rate.
     */
    public function getDeliveryRate(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        return round(($this->total_delivered / $this->total_sent) * 100, 2);
    }

    /**
     * Calculate bounce rate.
     */
    public function getBounceRate(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        return round(($this->total_bounced / $this->total_sent) * 100, 2);
    }

    /**
     * Calculate open rate.
     */
    public function getOpenRate(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        return round(($this->total_opened / $this->total_sent) * 100, 2);
    }

    /**
     * Calculate click rate (based on delivered).
     */
    public function getClickRate(): float
    {
        if ($this->total_delivered === 0) {
            return 0;
        }

        return round(($this->total_clicked / $this->total_delivered) * 100, 2);
    }

    /**
     * Calculate click-to-open rate.
     */
    public function getClickToOpenRate(): float
    {
        if ($this->total_opened === 0) {
            return 0;
        }

        return round(($this->total_clicked / $this->total_opened) * 100, 2);
    }

    /**
     * Calculate conversion rate.
     */
    public function getConversionRate(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        return round(($this->total_conversions / $this->total_sent) * 100, 2);
    }

    /**
     * Calculate unsubscribe rate.
     */
    public function getUnsubscribeRate(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        return round(($this->total_unsubscribed / $this->total_sent) * 100, 2);
    }

    /**
     * Calculate ROI (Return on Investment).
     */
    public function getROI(): float
    {
        if ($this->cost_spent <= 0) {
            return 0;
        }

        return round((($this->revenue_generated - $this->cost_spent) / $this->cost_spent) * 100, 2);
    }

    /**
     * Calculate ROAS (Return on Ad Spend).
     */
    public function getROAS(): float
    {
        if ($this->cost_spent <= 0) {
            return 0;
        }

        return round($this->revenue_generated / $this->cost_spent, 2);
    }

    /**
     * Calculate cost per acquisition (CPA).
     */
    public function getCostPerAcquisition(): float
    {
        if ($this->total_conversions === 0) {
            return 0;
        }

        return round($this->cost_spent / $this->total_conversions, 2);
    }

    /**
     * Calculate revenue per conversion.
     */
    public function getRevenuePerConversion(): float
    {
        if ($this->total_conversions === 0) {
            return 0;
        }

        return round($this->revenue_generated / $this->total_conversions, 2);
    }

    /**
     * Calculate cost per click (CPC).
     */
    public function getCostPerClick(): float
    {
        if ($this->total_clicked === 0) {
            return 0;
        }

        return round($this->cost_spent / $this->total_clicked, 2);
    }

    /**
     * Calculate cost per thousand impressions (CPM).
     */
    public function getCostPerMille(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        return round(($this->cost_spent / $this->total_sent) * 1000, 2);
    }

    /**
     * Get engagement rate (opens + clicks).
     */
    public function getEngagementRate(): float
    {
        if ($this->total_sent === 0) {
            return 0;
        }

        $engaged = $this->total_opened + $this->total_clicked;

        return round(($engaged / $this->total_sent) * 100, 2);
    }

    /**
     * Get net profit.
     */
    public function getNetProfit(): float
    {
        return round($this->revenue_generated - $this->cost_spent, 2);
    }

    /**
     * Get performance summary.
     */
    public function getPerformanceSummary(): array
    {
        return [
            'delivery' => [
                'sent' => $this->total_sent,
                'delivered' => $this->total_delivered,
                'bounced' => $this->total_bounced,
                'delivery_rate' => $this->getDeliveryRate(),
                'bounce_rate' => $this->getBounceRate(),
            ],
            'engagement' => [
                'opened' => $this->total_opened,
                'clicked' => $this->total_clicked,
                'open_rate' => $this->getOpenRate(),
                'click_rate' => $this->getClickRate(),
                'click_to_open_rate' => $this->getClickToOpenRate(),
                'engagement_rate' => $this->getEngagementRate(),
            ],
            'conversion' => [
                'conversions' => $this->total_conversions,
                'conversion_rate' => $this->getConversionRate(),
                'revenue_generated' => $this->revenue_generated,
                'revenue_per_conversion' => $this->getRevenuePerConversion(),
            ],
            'cost' => [
                'cost_spent' => $this->cost_spent,
                'cost_per_acquisition' => $this->getCostPerAcquisition(),
                'cost_per_click' => $this->getCostPerClick(),
                'cost_per_mille' => $this->getCostPerMille(),
            ],
            'roi' => [
                'roi_percentage' => $this->getROI(),
                'roas' => $this->getROAS(),
                'net_profit' => $this->getNetProfit(),
            ],
            'other' => [
                'unsubscribed' => $this->total_unsubscribed,
                'unsubscribe_rate' => $this->getUnsubscribeRate(),
            ],
        ];
    }

    /**
     * Get channel performance breakdown.
     */
    public function getChannelBreakdown(): array
    {
        return $this->channel_breakdown ?? [];
    }

    /**
     * Get hourly performance breakdown.
     */
    public function getHourlyBreakdown(): array
    {
        return $this->hourly_breakdown ?? [];
    }

    /**
     * Get daily performance breakdown.
     */
    public function getDailyBreakdown(): array
    {
        return $this->daily_breakdown ?? [];
    }

    /**
     * Update channel breakdown.
     */
    public function updateChannelBreakdown(string $channel, array $metrics): void
    {
        $breakdown = $this->channel_breakdown ?? [];
        $breakdown[$channel] = $metrics;
        $this->update(['channel_breakdown' => $breakdown]);
    }

    /**
     * Update hourly breakdown.
     */
    public function updateHourlyBreakdown(string $hour, array $metrics): void
    {
        $breakdown = $this->hourly_breakdown ?? [];
        $breakdown[$hour] = $metrics;
        $this->update(['hourly_breakdown' => $breakdown]);
    }

    /**
     * Update daily breakdown.
     */
    public function updateDailyBreakdown(string $date, array $metrics): void
    {
        $breakdown = $this->daily_breakdown ?? [];
        $breakdown[$date] = $metrics;
        $this->update(['daily_breakdown' => $breakdown]);
    }

    /**
     * Increment sent count.
     */
    public function incrementSent(int $count = 1): void
    {
        $this->increment('total_sent', $count);
    }

    /**
     * Increment delivered count.
     */
    public function incrementDelivered(int $count = 1): void
    {
        $this->increment('total_delivered', $count);
    }

    /**
     * Increment bounced count.
     */
    public function incrementBounced(int $count = 1): void
    {
        $this->increment('total_bounced', $count);
    }

    /**
     * Increment opened count.
     */
    public function incrementOpened(int $count = 1): void
    {
        $this->increment('total_opened', $count);
    }

    /**
     * Increment clicked count.
     */
    public function incrementClicked(int $count = 1): void
    {
        $this->increment('total_clicked', $count);
    }

    /**
     * Increment conversions count.
     */
    public function incrementConversions(int $count = 1): void
    {
        $this->increment('total_conversions', $count);
    }

    /**
     * Increment unsubscribed count.
     */
    public function incrementUnsubscribed(int $count = 1): void
    {
        $this->increment('total_unsubscribed', $count);
    }

    /**
     * Add revenue.
     */
    public function addRevenue(float $amount): void
    {
        $this->increment('revenue_generated', $amount);
    }

    /**
     * Add cost.
     */
    public function addCost(float $amount): void
    {
        $this->increment('cost_spent', $amount);
    }

    /**
     * Reset all metrics.
     */
    public function resetMetrics(): void
    {
        $this->update([
            'total_sent' => 0,
            'total_delivered' => 0,
            'total_bounced' => 0,
            'total_opened' => 0,
            'total_clicked' => 0,
            'total_conversions' => 0,
            'total_unsubscribed' => 0,
            'revenue_generated' => 0,
            'cost_spent' => 0,
            'channel_breakdown' => null,
            'hourly_breakdown' => null,
            'daily_breakdown' => null,
        ]);
    }
}
