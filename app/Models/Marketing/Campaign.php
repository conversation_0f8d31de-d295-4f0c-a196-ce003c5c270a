<?php

declare(strict_types=1);

namespace App\Models\Marketing;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Campaign Model
 * 
 * Represents marketing campaigns for customer acquisition and engagement.
 * Supports multi-channel campaigns (email, SMS, push notifications).
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property string $type
 * @property string $status
 * @property array $channels
 * @property array $content
 * @property array $settings
 * @property array|null $budget
 * @property string $created_by
 * @property \Illuminate\Support\Carbon|null $launched_at
 * @property string|null $launched_by
 * @property \Illuminate\Support\Carbon|null $paused_at
 * @property string|null $pause_reason
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Marketing\CampaignAudience> $audiences
 * @property-read int|null $audiences_count
 * @property-read \App\Models\User\User $creator
 * @property-read \App\Models\User\User|null $launcher
 * @property-read \App\Models\Marketing\CampaignMetric|null $metrics
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign completed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign draft()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign ofType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Campaign withChannel(string $channel)
 * @mixin \Eloquent
 */
class Campaign extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'type',
        'status',
        'channels',
        'content',
        'settings',
        'budget',
        'created_by',
        'launched_at',
        'launched_by',
        'paused_at',
        'pause_reason',
        'completed_at',
    ];

    protected $casts = [
        'channels' => 'array',
        'content' => 'array',
        'settings' => 'array',
        'budget' => 'array',
        'launched_at' => 'datetime',
        'paused_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Campaign types.
     */
    public const TYPE_EMAIL = 'email';

    public const TYPE_SMS = 'sms';

    public const TYPE_PUSH = 'push';

    public const TYPE_WHATSAPP = 'whatsapp';

    public const TYPE_MULTI_CHANNEL = 'multi_channel';

    /**
     * Campaign statuses.
     */
    public const STATUS_DRAFT = 'draft';

    public const STATUS_ACTIVE = 'active';

    public const STATUS_PAUSED = 'paused';

    public const STATUS_COMPLETED = 'completed';

    public const STATUS_CANCELLED = 'cancelled';

    /**
     * Available channels.
     */
    public const CHANNEL_EMAIL = 'email';

    public const CHANNEL_SMS = 'sms';

    public const CHANNEL_PUSH = 'push';

    public const CHANNEL_WHATSAPP = 'whatsapp';

    /**
     * Get the user who created the campaign.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who launched the campaign.
     */
    public function launcher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'launched_by');
    }

    /**
     * Get the campaign audiences.
     */
    public function audiences(): HasMany
    {
        return $this->hasMany(CampaignAudience::class);
    }

    /**
     * Get the campaign metrics.
     */
    public function metrics(): HasOne
    {
        return $this->hasOne(CampaignMetric::class);
    }

    /**
     * Scope for active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for draft campaigns.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    /**
     * Scope for completed campaigns.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for campaigns by type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for campaigns by channel.
     */
    public function scopeWithChannel($query, string $channel)
    {
        return $query->whereJsonContains('channels', $channel);
    }

    /**
     * Check if campaign is editable.
     */
    public function isEditable(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PAUSED]);
    }

    /**
     * Check if campaign can be launched.
     */
    public function canBeLaunched(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Check if campaign can be paused.
     */
    public function canBePaused(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if campaign is multi-channel.
     */
    public function isMultiChannel(): bool
    {
        return count($this->channels) > 1;
    }

    /**
     * Get campaign subject (for email campaigns).
     */
    public function getSubject(): ?string
    {
        return $this->content['subject'] ?? null;
    }

    /**
     * Get campaign message.
     */
    public function getMessage(): ?string
    {
        return $this->content['message'] ?? null;
    }

    /**
     * Get campaign template ID.
     */
    public function getTemplateId(): ?string
    {
        return $this->content['template_id'] ?? null;
    }

    /**
     * Get campaign variables.
     */
    public function getVariables(): array
    {
        return $this->content['variables'] ?? [];
    }

    /**
     * Check if campaign should send immediately.
     */
    public function shouldSendImmediately(): bool
    {
        return $this->settings['send_immediately'] ?? false;
    }

    /**
     * Get scheduled send time.
     */
    public function getScheduledAt(): ?\Illuminate\Support\Carbon
    {
        $scheduledAt = $this->settings['scheduled_at'] ?? null;

        return $scheduledAt ? \Illuminate\Support\Carbon::parse($scheduledAt) : null;
    }

    /**
     * Get campaign timezone.
     */
    public function getTimezone(): string
    {
        return $this->settings['timezone'] ?? 'Africa/Lagos';
    }

    /**
     * Get campaign frequency.
     */
    public function getFrequency(): string
    {
        return $this->settings['frequency'] ?? 'once';
    }

    /**
     * Check if A/B testing is enabled.
     */
    public function hasABTestEnabled(): bool
    {
        return $this->settings['ab_test_enabled'] ?? false;
    }

    /**
     * Get total budget.
     */
    public function getTotalBudget(): ?float
    {
        return $this->budget['total_budget'] ?? null;
    }

    /**
     * Get daily budget.
     */
    public function getDailyBudget(): ?float
    {
        return $this->budget['daily_budget'] ?? null;
    }

    /**
     * Get budget currency.
     */
    public function getCurrency(): string
    {
        return $this->budget['currency'] ?? 'NGN';
    }

    /**
     * Get campaign duration in days.
     */
    public function getDurationInDays(): ?int
    {
        if (! $this->launched_at) {
            return null;
        }

        $endDate = $this->completed_at ?? now();

        return $this->launched_at->diffInDays($endDate);
    }

    /**
     * Get campaign performance summary.
     */
    public function getPerformanceSummary(): array
    {
        $metrics = $this->metrics;

        if (! $metrics) {
            return [
                'sent' => 0,
                'delivered' => 0,
                'opened' => 0,
                'clicked' => 0,
                'conversions' => 0,
                'open_rate' => 0,
                'click_rate' => 0,
                'conversion_rate' => 0,
            ];
        }

        return [
            'sent' => $metrics->total_sent,
            'delivered' => $metrics->total_delivered,
            'opened' => $metrics->total_opened,
            'clicked' => $metrics->total_clicked,
            'conversions' => $metrics->total_conversions,
            'open_rate' => $metrics->total_sent > 0 ?
                round(($metrics->total_opened / $metrics->total_sent) * 100, 2) : 0,
            'click_rate' => $metrics->total_delivered > 0 ?
                round(($metrics->total_clicked / $metrics->total_delivered) * 100, 2) : 0,
            'conversion_rate' => $metrics->total_sent > 0 ?
                round(($metrics->total_conversions / $metrics->total_sent) * 100, 2) : 0,
        ];
    }

    /**
     * Get available campaign types.
     */
    public static function getAvailableTypes(): array
    {
        return [
            self::TYPE_EMAIL => 'Email Campaign',
            self::TYPE_SMS => 'SMS Campaign',
            self::TYPE_PUSH => 'Push Notification Campaign',
            self::TYPE_WHATSAPP => 'WhatsApp Campaign',
            self::TYPE_MULTI_CHANNEL => 'Multi-Channel Campaign',
        ];
    }

    /**
     * Get available campaign statuses.
     */
    public static function getAvailableStatuses(): array
    {
        return [
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_PAUSED => 'Paused',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
        ];
    }

    /**
     * Get available channels.
     */
    public static function getAvailableChannels(): array
    {
        return [
            self::CHANNEL_EMAIL => 'Email',
            self::CHANNEL_SMS => 'SMS',
            self::CHANNEL_PUSH => 'Push Notification',
            self::CHANNEL_WHATSAPP => 'WhatsApp',
        ];
    }
}
