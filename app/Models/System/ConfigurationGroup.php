<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Configuration Group Model
 * 
 * Groups related configuration settings together for better organization.
 *
 * @property string $id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property string|null $icon
 * @property int $sort_order
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ConfigurationSetting> $settings
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\ConfigurationSetting> $activeSettings
 * @property-read int|null $active_settings_count
 * @property-read int|null $settings_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationGroup active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationGroup bySlug(string $slug)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationGroup ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationGroup query()
 * @mixin \Eloquent
 */
class ConfigurationGroup extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get all settings in this group.
     */
    public function settings(): HasMany
    {
        return $this->hasMany(ConfigurationSetting::class, 'group_id');
    }

    /**
     * Get active settings in this group.
     */
    public function activeSettings(): HasMany
    {
        return $this->settings()->where('is_active', true);
    }

    /**
     * Scope for active groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered groups.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get group by slug.
     */
    public function scopeBySlug($query, string $slug)
    {
        return $query->where('slug', $slug);
    }
}
