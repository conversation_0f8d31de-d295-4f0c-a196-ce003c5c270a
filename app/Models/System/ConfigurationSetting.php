<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\ConfigurationType;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Configuration Setting Model
 * 
 * Individual configuration settings with validation and history tracking.
 *
 * @property string $id
 * @property string|null $group_id
 * @property string $key
 * @property string $name
 * @property string|null $description
 * @property ConfigurationType $type
 * @property mixed $value
 * @property mixed $default_value
 * @property array|null $validation_rules
 * @property array|null $options
 * @property bool $is_required
 * @property bool $is_active
 * @property bool $is_sensitive
 * @property string|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read ConfigurationGroup|null $group
 * @property-read User|null $updatedBy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ConfigurationHistory> $history
 * @property-read int|null $history_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting byKey(string $key)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting inGroup(string $groupId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationSetting required()
 * @mixin \Eloquent
 */
class ConfigurationSetting extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'group_id',
        'key',
        'name',
        'description',
        'type',
        'value',
        'default_value',
        'validation_rules',
        'options',
        'is_required',
        'is_active',
        'is_sensitive',
        'updated_by',
    ];

    protected $casts = [
        'type' => ConfigurationType::class,
        'value' => 'json',
        'default_value' => 'json',
        'validation_rules' => 'array',
        'options' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
        'is_sensitive' => 'boolean',
    ];

    protected $hidden = [
        'value' => 'is_sensitive',
    ];

    /**
     * Get the group this setting belongs to.
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(ConfigurationGroup::class, 'group_id');
    }

    /**
     * Get the user who last updated this setting.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the history of changes for this setting.
     */
    public function history(): HasMany
    {
        return $this->hasMany(ConfigurationHistory::class, 'setting_id');
    }

    /**
     * Scope for active settings.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by group.
     */
    public function scopeInGroup($query, string $groupId)
    {
        return $query->where('group_id', $groupId);
    }

    /**
     * Scope by key.
     */
    public function scopeByKey($query, string $key)
    {
        return $query->where('key', $key);
    }

    /**
     * Scope for required settings.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Get setting value with type casting.
     */
    public function getTypedValue()
    {
        if ($this->value === null) {
            return $this->default_value;
        }

        return match ($this->type) {
            ConfigurationType::BOOLEAN => (bool) $this->value,
            ConfigurationType::INTEGER => (int) $this->value,
            ConfigurationType::FLOAT => (float) $this->value,
            ConfigurationType::STRING, ConfigurationType::TEXT => (string) $this->value,
            ConfigurationType::JSON, ConfigurationType::ARRAY => $this->value,
            ConfigurationType::EMAIL => (string) $this->value,
            ConfigurationType::URL => (string) $this->value,
            ConfigurationType::PASSWORD => (string) $this->value,
            default => $this->value,
        };
    }

    /**
     * Set value with validation.
     */
    public function setTypedValue($value): void
    {
        // Store previous value for history
        $previousValue = $this->value;

        // Validate the value
        if ($this->validation_rules) {
            $validator = validator(['value' => $value], ['value' => $this->validation_rules]);
            if ($validator->fails()) {
                throw new \InvalidArgumentException('Invalid value: '.implode(', ', $validator->errors()->all()));
            }
        }

        // Set the value
        $this->value = $value;
        $this->updated_by = auth()->id();
        $this->save();

        // Create history record
        ConfigurationHistory::create([
            'setting_id' => $this->id,
            'previous_value' => $previousValue,
            'new_value' => $value,
            'changed_by' => auth()->id(),
            'change_reason' => 'Updated via admin panel',
        ]);
    }

    /**
     * Get a setting value by key.
     */
    public static function getValue(string $key, $default = null)
    {
        $setting = static::active()->byKey($key)->first();

        if (! $setting) {
            return $default;
        }

        return $setting->getTypedValue();
    }

    /**
     * Set a setting value by key.
     */
    public static function setValue(string $key, $value): void
    {
        $setting = static::byKey($key)->first();

        if (! $setting) {
            throw new \InvalidArgumentException("Setting with key '{$key}' not found");
        }

        $setting->setTypedValue($value);
    }

    /**
     * Check if setting is editable.
     */
    public function isEditable(): bool
    {
        return $this->is_active && ! $this->is_sensitive;
    }

    /**
     * Get display value (masked for sensitive settings).
     */
    public function getDisplayValue(): string
    {
        if ($this->is_sensitive && $this->value) {
            return '••••••••';
        }

        $value = $this->getTypedValue();

        return match ($this->type) {
            ConfigurationType::BOOLEAN => $value ? 'Yes' : 'No',
            ConfigurationType::ARRAY, ConfigurationType::JSON => json_encode($value),
            ConfigurationType::PASSWORD => '••••••••',
            default => (string) $value,
        };
    }
}
