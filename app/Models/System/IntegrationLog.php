<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Integration Log Model
 * 
 * Tracks integration API calls and responses for monitoring and debugging.
 *
 * @property string $id
 * @property string $integration_id
 * @property string $method
 * @property string $endpoint
 * @property array|null $request_data
 * @property array|null $response_data
 * @property int $response_code
 * @property string $status
 * @property float $duration_ms
 * @property string|null $error_message
 * @property string|null $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Integration $integration
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog recent(int $hours = 24)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog slow(float $thresholdMs = '5000')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IntegrationLog successful()
 * @mixin \Eloquent
 */
class IntegrationLog extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'integration_id',
        'method',
        'endpoint',
        'request_data',
        'response_data',
        'response_code',
        'status',
        'duration_ms',
        'error_message',
        'user_id',
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'response_code' => 'integer',
        'duration_ms' => 'float',
    ];

    /**
     * Get the integration this log belongs to.
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(Integration::class);
    }

    /**
     * Scope for successful requests.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * Scope for failed requests.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'error');
    }

    /**
     * Scope for recent logs.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for slow requests.
     */
    public function scopeSlow($query, float $thresholdMs = 5000)
    {
        return $query->where('duration_ms', '>', $thresholdMs);
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDuration(): string
    {
        if ($this->duration_ms < 1000) {
            return round($this->duration_ms, 2).'ms';
        }

        return round($this->duration_ms / 1000, 2).'s';
    }

    /**
     * Check if request was successful.
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'success' && $this->response_code >= 200 && $this->response_code < 300;
    }

    /**
     * Get sanitized request data (remove sensitive information).
     */
    public function getSanitizedRequestData(): array
    {
        if (! $this->request_data) {
            return [];
        }

        $sensitiveKeys = ['password', 'token', 'api_key', 'secret', 'authorization'];
        $sanitized = $this->request_data;

        foreach ($sensitiveKeys as $key) {
            if (isset($sanitized[$key])) {
                $sanitized[$key] = '***REDACTED***';
            }
        }

        return $sanitized;
    }
}
