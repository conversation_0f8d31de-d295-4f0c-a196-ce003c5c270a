<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\IntegrationStatus;
use App\Enums\System\IntegrationType;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Integration Model
 * 
 * Manages external service integrations and their configurations.
 *
 * @property string $id
 * @property string $name
 * @property string $slug
 * @property IntegrationType $type
 * @property IntegrationStatus $status
 * @property string|null $description
 * @property string|null $provider
 * @property string|null $version
 * @property array|null $configuration
 * @property array|null $credentials
 * @property array|null $settings
 * @property array|null $endpoints
 * @property bool $is_active
 * @property bool $is_sandbox
 * @property string|null $last_tested_at
 * @property string|null $last_error
 * @property string|null $created_by
 * @property string|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read User|null $createdBy
 * @property-read User|null $updatedBy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, IntegrationLog> $logs
 * @property-read int|null $logs_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration ofType(\App\Enums\System\IntegrationType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration production()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration sandbox()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Integration withStatus(\App\Enums\System\IntegrationStatus $status)
 * @mixin \Eloquent
 */
class Integration extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'slug',
        'type',
        'status',
        'description',
        'provider',
        'version',
        'configuration',
        'credentials',
        'settings',
        'endpoints',
        'is_active',
        'is_sandbox',
        'last_tested_at',
        'last_error',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'type' => IntegrationType::class,
        'status' => IntegrationStatus::class,
        'configuration' => 'array',
        'credentials' => 'array',
        'settings' => 'array',
        'endpoints' => 'array',
        'is_active' => 'boolean',
        'is_sandbox' => 'boolean',
        'last_tested_at' => 'datetime',
    ];

    protected $hidden = [
        'credentials',
    ];

    /**
     * Get the user who created this integration.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this integration.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get all logs for this integration.
     */
    public function logs(): HasMany
    {
        return $this->hasMany(IntegrationLog::class);
    }

    /**
     * Scope for active integrations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by integration type.
     */
    public function scopeOfType($query, IntegrationType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by status.
     */
    public function scopeWithStatus($query, IntegrationStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for production integrations.
     */
    public function scopeProduction($query)
    {
        return $query->where('is_sandbox', false);
    }

    /**
     * Scope for sandbox integrations.
     */
    public function scopeSandbox($query)
    {
        return $query->where('is_sandbox', true);
    }

    /**
     * Check if integration is connected.
     */
    public function isConnected(): bool
    {
        return $this->status === IntegrationStatus::CONNECTED;
    }

    /**
     * Check if integration has errors.
     */
    public function hasErrors(): bool
    {
        return $this->status === IntegrationStatus::ERROR || ! empty($this->last_error);
    }

    /**
     * Get masked credentials for display.
     */
    public function getMaskedCredentials(): array
    {
        if (! $this->credentials) {
            return [];
        }

        $masked = [];
        foreach ($this->credentials as $key => $value) {
            if (is_string($value) && strlen($value) > 4) {
                $masked[$key] = substr($value, 0, 4).str_repeat('*', strlen($value) - 4);
            } else {
                $masked[$key] = '****';
            }
        }

        return $masked;
    }

    /**
     * Get integration health status.
     */
    public function getHealthStatus(): array
    {
        $recentLogs = $this->logs()
            ->where('created_at', '>=', now()->subHours(24))
            ->get();

        $totalRequests = $recentLogs->count();
        $successfulRequests = $recentLogs->where('status', 'success')->count();
        $failedRequests = $recentLogs->where('status', 'error')->count();

        $successRate = $totalRequests > 0 ? ($successfulRequests / $totalRequests) * 100 : 0;

        return [
            'status' => $this->status->value,
            'is_healthy' => $this->isConnected() && $successRate >= 95,
            'success_rate' => round($successRate, 2),
            'total_requests_24h' => $totalRequests,
            'successful_requests_24h' => $successfulRequests,
            'failed_requests_24h' => $failedRequests,
            'last_tested' => $this->last_tested_at,
            'last_error' => $this->last_error,
        ];
    }

    /**
     * Update integration status.
     */
    public function updateStatus(IntegrationStatus $status, ?string $error = null): void
    {
        $this->update([
            'status' => $status,
            'last_error' => $error,
            'last_tested_at' => now(),
            'updated_by' => auth()->id(),
        ]);
    }

    /**
     * Test integration connection.
     */
    public function testConnection(): bool
    {
        try {
            // This would be implemented based on integration type
            // For now, just update the last_tested_at timestamp
            $this->updateStatus(IntegrationStatus::CONNECTED);

            return true;
        } catch (\Exception $e) {
            $this->updateStatus(IntegrationStatus::ERROR, $e->getMessage());

            return false;
        }
    }

    /**
     * Get configuration with defaults.
     */
    public function getConfigurationAttribute($value): array
    {
        $config = $value ? json_decode($value, true) : [];

        return array_merge($this->getDefaultConfiguration(), $config);
    }

    /**
     * Get default configuration based on integration type.
     */
    private function getDefaultConfiguration(): array
    {
        return match ($this->type) {
            IntegrationType::PAYMENT => [
                'timeout' => 30,
                'retry_attempts' => 3,
                'webhook_enabled' => true,
                'auto_capture' => false,
            ],
            IntegrationType::SMS => [
                'timeout' => 10,
                'retry_attempts' => 2,
                'delivery_reports' => true,
                'unicode_support' => true,
            ],
            IntegrationType::EMAIL => [
                'timeout' => 30,
                'retry_attempts' => 3,
                'tracking_enabled' => true,
                'bounce_handling' => true,
            ],
            IntegrationType::MAPS => [
                'timeout' => 15,
                'cache_duration' => 3600,
                'geocoding_enabled' => true,
                'directions_enabled' => true,
            ],
            default => [
                'timeout' => 30,
                'retry_attempts' => 3,
            ],
        };
    }
}
