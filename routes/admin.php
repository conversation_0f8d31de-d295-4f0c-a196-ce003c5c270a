<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Central\Admin\AddressController as AdminAddressController;
use App\Http\Controllers\Api\V1\Central\Admin\AnalyticsController as AdminAnalyticsController;
use App\Http\Controllers\Api\V1\Central\Admin\ApiController as AdminApiController;
use App\Http\Controllers\Api\V1\Central\Admin\AuditController as AdminAuditController;
use App\Http\Controllers\Api\V1\Central\Admin\BackupController as AdminBackupController;
use App\Http\Controllers\Api\V1\Central\Admin\BusinessManagementController as AdminBusinessManagementController;
use App\Http\Controllers\Api\V1\Central\Admin\CampaignController as AdminCampaignController;
use App\Http\Controllers\Api\V1\Central\Admin\AdminCategoryController;
use App\Http\Controllers\Api\V1\Central\Admin\CollectionController as AdminCollectionController;
use App\Http\Controllers\Api\V1\Central\Admin\ComplianceController as AdminComplianceController;
use App\Http\Controllers\Api\V1\Central\Admin\ConfigController as AdminConfigController;
use App\Http\Controllers\Api\V1\Central\Admin\ContentController as AdminContentController;
use App\Http\Controllers\Api\V1\Central\Admin\AdminDeliveryController;
use App\Http\Controllers\Api\V1\Central\Admin\FeatureController as AdminFeatureController;
use App\Http\Controllers\Api\V1\Central\Admin\FeedbackController as AdminFeedbackController;
use App\Http\Controllers\Api\V1\Central\Admin\FinancialController;
use App\Http\Controllers\Api\V1\Central\Admin\IntegrationController as AdminIntegrationController;
use App\Http\Controllers\Api\V1\Central\Admin\InventoryController as AdminInventoryController;
use App\Http\Controllers\Api\V1\Central\Admin\KycController as AdminKycController;
use App\Http\Controllers\Api\V1\Central\Admin\LocationController as AdminLocationController;
use App\Http\Controllers\Api\V1\Central\Admin\MacroController as AdminMacroController;
use App\Http\Controllers\Api\V1\Central\Admin\MaintenanceController as AdminMaintenanceController;
use App\Http\Controllers\Api\V1\Central\Admin\NotificationController as AdminNotificationController;
use App\Http\Controllers\Api\V1\Central\Admin\AdminOrderController;
use App\Http\Controllers\Api\V1\Central\Admin\AdminPaymentController;
use App\Http\Controllers\Api\V1\Central\Admin\PermissionController as AdminPermissionController;
use App\Http\Controllers\Api\V1\Central\Admin\PlanController as AdminPlanController;
use App\Http\Controllers\Api\V1\Central\Admin\AdminProductController;
use App\Http\Controllers\Api\V1\Central\Admin\PromoController as AdminPromoController;
use App\Http\Controllers\Api\V1\Central\Admin\ProviderManagementController as AdminProviderManagementController;
use App\Http\Controllers\Api\V1\Central\Admin\ReportController as AdminReportController;
use App\Http\Controllers\Api\V1\Central\Admin\ReviewController as AdminReviewController;
use App\Http\Controllers\Api\V1\Central\Admin\SecurityController as AdminSecurityController;
use App\Http\Controllers\Api\V1\Central\Admin\SettingsController as AdminSettingsController;
use App\Http\Controllers\Api\V1\Central\Admin\StaffActivityController;
use App\Http\Controllers\Api\V1\Central\Admin\SubscriptionController as AdminSubscriptionController;
use App\Http\Controllers\Api\V1\Central\Admin\SupportController as AdminSupportController;
use App\Http\Controllers\Api\V1\Central\Admin\SystemController as AdminSystemController;
use App\Http\Controllers\Api\V1\Central\Admin\TenantController;
use App\Http\Controllers\Api\V1\Central\Admin\ThemeController as AdminThemeController;
use App\Http\Controllers\Api\V1\Central\Admin\UserController as AdminUserController;
use App\Http\Controllers\Api\V1\Central\Admin\WorkflowController as AdminWorkflowController;
use App\Http\Controllers\Api\V1\Central\Admin\ZoneController as AdminZoneController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin API Routes
|--------------------------------------------------------------------------
|
| These routes are for platform administrators only.
| All routes require authentication and admin role.
| Prefix: /api/v1/admin
|
*/

// All admin routes require authentication and admin role
Route::middleware(['auth:sanctum', 'role:platform-admin|super-admin'])->group(function () {
    
    // Tenant management - specific routes first to avoid conflicts
    Route::get('tenants/statistics', [TenantController::class, 'statistics']);
    Route::get('tenants/check-subdomain', [TenantController::class, 'checkSubdomain']);
    Route::post('tenants/{tenant}/restore', [TenantController::class, 'restore']);
    Route::put('tenants/{tenant}/status', [TenantController::class, 'updateStatus']);
    Route::apiResource('tenants', TenantController::class);

    // Business management (cross-tenant)
    Route::apiResource('businesses', AdminBusinessManagementController::class);
    Route::post('businesses/{business}/restore', [AdminBusinessManagementController::class, 'restore']);
    Route::post('businesses/{business}/activate', [AdminBusinessManagementController::class, 'activate']);
    Route::post('businesses/{business}/suspend', [AdminBusinessManagementController::class, 'suspend']);
    Route::post('businesses/{business}/verify', [AdminBusinessManagementController::class, 'verify']);

    // Business admin management routes
    Route::get('businesses/{business}/admins', [AdminBusinessManagementController::class, 'getAdmins']);
    Route::post('businesses/{business}/assign-admin', [AdminBusinessManagementController::class, 'assignAdmin']);
    Route::post('businesses/{business}/transfer-admin', [AdminBusinessManagementController::class, 'transferOwnership']);
    Route::put('businesses/{business}/admins/{user}/role', [AdminBusinessManagementController::class, 'updateAdminRole']);
    Route::delete('businesses/{business}/admins/{user}', [AdminBusinessManagementController::class, 'removeAdmin']);

    // Provider management (cross-tenant)
    Route::apiResource('providers', AdminProviderManagementController::class);
    Route::post('providers/{provider}/activate', [AdminProviderManagementController::class, 'activate']);
    Route::post('providers/{provider}/suspend', [AdminProviderManagementController::class, 'suspend']);
    Route::post('providers/{provider}/verify', [AdminProviderManagementController::class, 'verify']);

    // User management (cross-tenant)
    Route::apiResource('users', AdminUserController::class);
    Route::post('users/{user}/activate', [AdminUserController::class, 'activate']);
    Route::post('users/{user}/suspend', [AdminUserController::class, 'suspend']);
    Route::post('users/{user}/verify-email', [AdminUserController::class, 'verifyEmail']);
    Route::post('users/{user}/verify-phone', [AdminUserController::class, 'verifyPhone']);

    // Staff activity tracking and audit logs
    Route::prefix('activities')->group(function () {
        Route::get('/', [StaffActivityController::class, 'index']);
        Route::get('/statistics', [StaffActivityController::class, 'statistics']);
        Route::get('/critical', [StaffActivityController::class, 'critical']);
        Route::get('/{activity}', [StaffActivityController::class, 'show']);
    });

    // Financial management
    Route::prefix('financial')->group(function () {
        Route::get('/platform/summary', [FinancialController::class, 'platformSummary']);
        Route::get('/platform/account', [FinancialController::class, 'platformAccount']);
        Route::get('/businesses/{business}/summary', [FinancialController::class, 'businessSummary']);
        Route::get('/businesses/{business}/account', [FinancialController::class, 'businessAccount']);
        Route::get('/providers/{provider}/summary', [FinancialController::class, 'providerSummary']);
        Route::get('/providers/{provider}/account', [FinancialController::class, 'providerAccount']);
    });

    // Analytics management
    Route::prefix('analytics')->group(function () {
        Route::get('/overview', [AdminAnalyticsController::class, 'overview']);
        Route::get('/real-time-dashboard', [AdminAnalyticsController::class, 'realTimeDashboard']);
        Route::get('/business-performance', [AdminAnalyticsController::class, 'businessPerformance']);
        Route::get('/revenue-analytics', [AdminAnalyticsController::class, 'revenueAnalytics']);
        Route::get('/user-engagement', [AdminAnalyticsController::class, 'userEngagement']);
        Route::get('/operational-metrics', [AdminAnalyticsController::class, 'operationalMetrics']);
        Route::get('/subscriptions', [AdminAnalyticsController::class, 'subscriptions']);
        Route::get('/financial', [AdminAnalyticsController::class, 'financial']);
        Route::get('/usage', [AdminAnalyticsController::class, 'usage']);
        Route::post('/custom-report', [AdminAnalyticsController::class, 'customReport']);
        Route::post('/export', [AdminAnalyticsController::class, 'export']);
        Route::get('/download/{filename}', [AdminAnalyticsController::class, 'download'])->name('admin.analytics.download');
    });

    // Campaign management
    Route::prefix('campaigns')->group(function () {
        Route::get('/', [AdminCampaignController::class, 'index']);
        Route::get('/analytics', [AdminCampaignController::class, 'analytics']);
        Route::post('/', [AdminCampaignController::class, 'store']);
        Route::get('/{campaign}', [AdminCampaignController::class, 'show']);
        Route::put('/{campaign}', [AdminCampaignController::class, 'update']);
        Route::post('/{campaign}/launch', [AdminCampaignController::class, 'launch']);
        Route::post('/{campaign}/pause', [AdminCampaignController::class, 'pause']);
        Route::get('/{campaign}/recipients', [AdminCampaignController::class, 'recipients']);
        Route::put('/{campaign}/metrics', [AdminCampaignController::class, 'updateMetrics']);
        Route::get('/{campaign}/ab-test-results', [AdminCampaignController::class, 'abTestResults']);
        Route::delete('/{campaign}', [AdminCampaignController::class, 'destroy']);
    });

    // Promotion management
    Route::prefix('promotions')->group(function () {
        Route::get('/', [AdminPromoController::class, 'index']);
        Route::post('/', [AdminPromoController::class, 'store']);
        Route::get('/analytics', [AdminPromoController::class, 'analytics']);
        Route::post('/bulk-operations', [AdminPromoController::class, 'bulkOperations']);
        Route::get('/{promotion}', [AdminPromoController::class, 'show']);
        Route::put('/{promotion}', [AdminPromoController::class, 'update']);
        Route::delete('/{promotion}', [AdminPromoController::class, 'destroy']);
        Route::put('/{promotion}/status', [AdminPromoController::class, 'toggleStatus']);
        Route::get('/{promotion}/usage', [AdminPromoController::class, 'usage']);
        Route::get('/{promotion}/fraud-detection', [AdminPromoController::class, 'fraudDetection']);
    });

    // Review management
    Route::prefix('reviews')->group(function () {
        Route::get('/', [AdminReviewController::class, 'index']);
        Route::get('/analytics', [AdminReviewController::class, 'analytics']);
        Route::get('/pending', [AdminReviewController::class, 'pendingReviews']);
        Route::get('/fraud-detection', [AdminReviewController::class, 'fraudDetection']);
        Route::post('/bulk-operations', [AdminReviewController::class, 'bulkOperations']);
        Route::get('/business/{businessId}/reputation', [AdminReviewController::class, 'businessReputation']);
        Route::get('/{review}', [AdminReviewController::class, 'show']);
        Route::post('/{review}/approve', [AdminReviewController::class, 'approve']);
        Route::post('/{review}/reject', [AdminReviewController::class, 'reject']);
        Route::delete('/{review}', [AdminReviewController::class, 'destroy']);
    });

    // Inventory management
    Route::prefix('inventory')->group(function () {
        Route::get('/', [AdminInventoryController::class, 'index']);
        Route::get('/analytics', [AdminInventoryController::class, 'analytics']);
        Route::get('/movements', [AdminInventoryController::class, 'movements']);
        Route::get('/low-stock-alerts', [AdminInventoryController::class, 'lowStockAlerts']);
        Route::get('/out-of-stock', [AdminInventoryController::class, 'outOfStock']);
        Route::post('/bulk-operations', [AdminInventoryController::class, 'bulkOperations']);
        Route::get('/business/{businessId}/report', [AdminInventoryController::class, 'businessReport']);
    });

    // Report management
    Route::prefix('reports')->group(function () {
        Route::get('/platform-overview', [AdminReportController::class, 'platformOverview']);
        Route::get('/business-performance', [AdminReportController::class, 'businessPerformance']);
        Route::get('/financial-analytics', [AdminReportController::class, 'financialAnalytics']);
        Route::get('/operational-efficiency', [AdminReportController::class, 'operationalEfficiency']);
        Route::post('/custom', [AdminReportController::class, 'customReport']);
        Route::post('/export', [AdminReportController::class, 'exportReport']);
        Route::get('/templates', [AdminReportController::class, 'templates']);
        Route::post('/schedule', [AdminReportController::class, 'scheduleReport']);
    });

    // Backup management
    Route::prefix('backups')->group(function () {
        Route::get('/', [AdminBackupController::class, 'index']);
        Route::post('/', [AdminBackupController::class, 'store']);
        Route::get('/analytics', [AdminBackupController::class, 'analytics']);
        Route::post('/schedule', [AdminBackupController::class, 'schedule']);
        Route::post('/bulk-operations', [AdminBackupController::class, 'bulkOperations']);
        Route::post('/{backup}/verify', [AdminBackupController::class, 'verify']);
        Route::post('/{backup}/restore', [AdminBackupController::class, 'restore']);
        Route::delete('/{backup}', [AdminBackupController::class, 'destroy']);
    });

    // Maintenance management
    Route::prefix('maintenance')->group(function () {
        Route::get('/', [AdminMaintenanceController::class, 'index']);
        Route::post('/', [AdminMaintenanceController::class, 'store']);
        Route::get('/analytics', [AdminMaintenanceController::class, 'analytics']);
        Route::get('/system-status', [AdminMaintenanceController::class, 'systemStatus']);
        Route::post('/{window}/start', [AdminMaintenanceController::class, 'start']);
        Route::post('/{window}/complete', [AdminMaintenanceController::class, 'complete']);
        Route::post('/{window}/cancel', [AdminMaintenanceController::class, 'cancel']);
        Route::post('/{window}/rollback', [AdminMaintenanceController::class, 'rollback']);
    });

    // Compliance management
    Route::prefix('compliance')->group(function () {
        Route::get('/', [AdminComplianceController::class, 'index']);
        Route::post('/', [AdminComplianceController::class, 'store']);
        Route::get('/analytics', [AdminComplianceController::class, 'analytics']);
        Route::post('/run-check', [AdminComplianceController::class, 'runCheck']);
        Route::post('/generate-report', [AdminComplianceController::class, 'generateReport']);
        Route::post('/data-subject-request', [AdminComplianceController::class, 'handleDataSubjectRequest']);
        Route::post('/bulk-operations', [AdminComplianceController::class, 'bulkOperations']);
        Route::put('/{rule}', [AdminComplianceController::class, 'update']);
        Route::delete('/{rule}', [AdminComplianceController::class, 'destroy']);
    });

    // Feedback management
    Route::prefix('feedback')->group(function () {
        Route::get('/', [AdminFeedbackController::class, 'index']);
        Route::post('/', [AdminFeedbackController::class, 'store']);
        Route::get('/analytics', [AdminFeedbackController::class, 'analytics']);
        Route::post('/surveys', [AdminFeedbackController::class, 'createSurvey']);
        Route::post('/bulk-operations', [AdminFeedbackController::class, 'bulkOperations']);
        Route::put('/{feedback}', [AdminFeedbackController::class, 'update']);
        Route::post('/{feedback}/response', [AdminFeedbackController::class, 'addResponse']);
        Route::post('/{feedback}/vote', [AdminFeedbackController::class, 'vote']);
    });

    // Theme management
    Route::prefix('themes')->group(function () {
        Route::get('/', [AdminThemeController::class, 'index']);
        Route::post('/', [AdminThemeController::class, 'store']);
        Route::get('/analytics', [AdminThemeController::class, 'analytics']);
        Route::post('/import', [AdminThemeController::class, 'import']);
        Route::put('/{theme}', [AdminThemeController::class, 'update']);
        Route::post('/{theme}/activate', [AdminThemeController::class, 'activate']);
        Route::get('/{theme}/preview', [AdminThemeController::class, 'preview']);
        Route::get('/{theme}/export', [AdminThemeController::class, 'export']);
        Route::delete('/{theme}', [AdminThemeController::class, 'destroy']);
    });

    // Workflow management
    Route::prefix('workflows')->group(function () {
        Route::get('/', [AdminWorkflowController::class, 'index']);
        Route::post('/', [AdminWorkflowController::class, 'store']);
        Route::get('/analytics', [AdminWorkflowController::class, 'analytics']);
        Route::post('/bulk-operations', [AdminWorkflowController::class, 'bulkOperations']);
        Route::put('/{workflow}', [AdminWorkflowController::class, 'update']);
        Route::post('/{workflow}/execute', [AdminWorkflowController::class, 'execute']);
        Route::post('/{workflow}/toggle', [AdminWorkflowController::class, 'toggle']);
        Route::get('/{workflow}/executions', [AdminWorkflowController::class, 'executions']);
        Route::delete('/{workflow}', [AdminWorkflowController::class, 'destroy']);
    });

    // Macro management
    Route::prefix('macros')->group(function () {
        Route::get('/', [AdminMacroController::class, 'index']);
        Route::post('/', [AdminMacroController::class, 'store']);
        Route::get('/analytics', [AdminMacroController::class, 'analytics']);
        Route::get('/templates', [AdminMacroController::class, 'templates']);
        Route::post('/from-template', [AdminMacroController::class, 'createFromTemplate']);
        Route::post('/bulk-operations', [AdminMacroController::class, 'bulkOperations']);
        Route::put('/{macro}', [AdminMacroController::class, 'update']);
        Route::post('/{macro}/execute', [AdminMacroController::class, 'execute']);
        Route::post('/{macro}/toggle', [AdminMacroController::class, 'toggle']);
        Route::delete('/{macro}', [AdminMacroController::class, 'destroy']);
    });

    // KYC management
    Route::prefix('kyc')->group(function () {
        Route::get('/overview', [AdminKycController::class, 'overview']);
        Route::get('/pending-reviews', [AdminKycController::class, 'pendingReviews']);
        Route::get('/users/{user}/documents', [AdminKycController::class, 'userDocuments']);
        Route::post('/documents/{document}/approve', [AdminKycController::class, 'approveDocument']);
        Route::post('/documents/{document}/reject', [AdminKycController::class, 'rejectDocument']);
        Route::get('/statistics', [AdminKycController::class, 'statistics']);
    });

    // Subscription management
    Route::prefix('subscriptions')->group(function () {
        Route::get('/overview', [AdminSubscriptionController::class, 'overview']);
        Route::get('/', [AdminSubscriptionController::class, 'index']);
        Route::get('/{subscription}', [AdminSubscriptionController::class, 'show']);
        Route::put('/{subscription}/status', [AdminSubscriptionController::class, 'updateStatus']);
        Route::get('/analytics', [AdminSubscriptionController::class, 'analytics']);
        Route::get('/plans', [AdminSubscriptionController::class, 'plans']);
    });

    // Security management
    Route::prefix('security')->group(function () {
        Route::get('/overview', [AdminSecurityController::class, 'overview']);
        Route::get('/fraud-logs', [AdminSecurityController::class, 'fraudLogs']);
        Route::get('/audit-logs', [AdminSecurityController::class, 'auditLogs']);
        Route::get('/suspicious-users', [AdminSecurityController::class, 'suspiciousUsers']);
        Route::post('/users/{user}/block', [AdminSecurityController::class, 'blockUser']);
        Route::get('/analytics', [AdminSecurityController::class, 'analytics']);
    });

    // System management
    Route::prefix('system')->group(function () {
        Route::get('/health', [AdminSystemController::class, 'health']);
        Route::get('/configuration', [AdminSystemController::class, 'configuration']);
        Route::post('/clear-cache', [AdminSystemController::class, 'clearCache']);
        Route::post('/maintenance', [AdminSystemController::class, 'maintenance']);
        Route::get('/logs', [AdminSystemController::class, 'logs']);
        Route::get('/queue-status', [AdminSystemController::class, 'queueStatus']);
        Route::post('/restart-queue', [AdminSystemController::class, 'restartQueue']);
    });

    // Subscription plan management
    Route::prefix('plans')->group(function () {
        Route::get('/', [AdminPlanController::class, 'index']);
        Route::post('/', [AdminPlanController::class, 'store']);
        Route::get('/{plan}', [AdminPlanController::class, 'show']);
        Route::put('/{plan}', [AdminPlanController::class, 'update']);
        Route::delete('/{plan}', [AdminPlanController::class, 'destroy']);
        Route::put('/{plan}/status', [AdminPlanController::class, 'toggleStatus']);
        Route::put('/{plan}/pricing', [AdminPlanController::class, 'managePricing']);
        Route::put('/{plan}/features', [AdminPlanController::class, 'manageFeatures']);
        Route::get('/{plan}/analytics', [AdminPlanController::class, 'analytics']);
        Route::post('/{plan}/clone', [AdminPlanController::class, 'clone']);
    });

    // Category management
    Route::prefix('categories')->group(function () {
        Route::get('/', [AdminCategoryController::class, 'index']);
        Route::post('/', [AdminCategoryController::class, 'store']);
        Route::get('/analytics', [AdminCategoryController::class, 'analytics']);
        Route::post('/bulk-operations', [AdminCategoryController::class, 'bulkOperations']);
        Route::get('/{category}', [AdminCategoryController::class, 'show']);
        Route::put('/{category}', [AdminCategoryController::class, 'update']);
        Route::delete('/{category}', [AdminCategoryController::class, 'destroy']);
        Route::put('/{category}/status', [AdminCategoryController::class, 'toggleStatus']);
        Route::get('/{category}/products', [AdminCategoryController::class, 'products']);
    });

    // Product management
    Route::prefix('products')->group(function () {
        Route::get('/', [AdminProductController::class, 'index']);
        Route::get('/analytics', [AdminProductController::class, 'analytics']);
        Route::get('/trending', [AdminProductController::class, 'trending']);
        Route::post('/bulk-operations', [AdminProductController::class, 'bulkOperations']);
        Route::get('/{product}', [AdminProductController::class, 'show']);
        Route::put('/{product}/status', [AdminProductController::class, 'updateStatus']);
        Route::get('/{product}/reviews', [AdminProductController::class, 'reviews']);
    });

    // Order management
    Route::prefix('orders')->group(function () {
        Route::get('/', [AdminOrderController::class, 'index']);
        Route::get('/analytics', [AdminOrderController::class, 'analytics']);
        Route::get('/real-time', [AdminOrderController::class, 'realTime']);
        Route::post('/bulk-operations', [AdminOrderController::class, 'bulkOperations']);
        Route::get('/{order}', [AdminOrderController::class, 'show']);
        Route::put('/{order}/status', [AdminOrderController::class, 'updateStatus']);
        Route::post('/{order}/refund', [AdminOrderController::class, 'processRefund']);
    });

    // Delivery management
    Route::prefix('deliveries')->group(function () {
        Route::get('/', [AdminDeliveryController::class, 'index']);
        Route::get('/analytics', [AdminDeliveryController::class, 'analytics']);
        Route::get('/real-time', [AdminDeliveryController::class, 'realTime']);
        Route::post('/bulk-operations', [AdminDeliveryController::class, 'bulkOperations']);
        Route::get('/{delivery}', [AdminDeliveryController::class, 'show']);
        Route::put('/{delivery}/status', [AdminDeliveryController::class, 'updateStatus']);
        Route::post('/{delivery}/reassign', [AdminDeliveryController::class, 'reassign']);
    });

    // Payment management
    Route::prefix('payments')->group(function () {
        Route::get('/', [AdminPaymentController::class, 'index']);
        Route::get('/analytics', [AdminPaymentController::class, 'analytics']);
        Route::get('/disputes', [AdminPaymentController::class, 'disputes']);
        Route::post('/bulk-operations', [AdminPaymentController::class, 'bulkOperations']);
        Route::get('/{payment}', [AdminPaymentController::class, 'show']);
        Route::post('/{payment}/refund', [AdminPaymentController::class, 'refund']);
        Route::post('/{payment}/dispute', [AdminPaymentController::class, 'handleDispute']);
    });

    // Notification management
    Route::prefix('notifications')->group(function () {
        Route::get('/', [AdminNotificationController::class, 'index']);
        Route::post('/', [AdminNotificationController::class, 'store']);
        Route::get('/analytics', [AdminNotificationController::class, 'analytics']);
        Route::post('/broadcast', [AdminNotificationController::class, 'broadcast']);
        Route::post('/bulk-operations', [AdminNotificationController::class, 'bulkOperations']);
        Route::get('/{notification}', [AdminNotificationController::class, 'show']);
        Route::put('/{notification}', [AdminNotificationController::class, 'update']);
        Route::delete('/{notification}', [AdminNotificationController::class, 'destroy']);
    });

    // Support management
    Route::prefix('support')->group(function () {
        Route::get('/', [AdminSupportController::class, 'index']);
        Route::get('/analytics', [AdminSupportController::class, 'analytics']);
        Route::get('/pending', [AdminSupportController::class, 'pending']);
        Route::post('/bulk-operations', [AdminSupportController::class, 'bulkOperations']);
        Route::get('/{ticket}', [AdminSupportController::class, 'show']);
        Route::put('/{ticket}', [AdminSupportController::class, 'update']);
        Route::post('/{ticket}/assign', [AdminSupportController::class, 'assign']);
        Route::post('/{ticket}/close', [AdminSupportController::class, 'close']);
    });

    // Settings management
    Route::prefix('settings')->group(function () {
        Route::get('/', [AdminSettingsController::class, 'index']);
        Route::put('/', [AdminSettingsController::class, 'update']);
        Route::get('/platform', [AdminSettingsController::class, 'platform']);
        Route::put('/platform', [AdminSettingsController::class, 'updatePlatform']);
        Route::post('/reset', [AdminSettingsController::class, 'reset']);
        Route::get('/export', [AdminSettingsController::class, 'export']);
        Route::post('/import', [AdminSettingsController::class, 'import']);
    });

    // Permission management
    Route::prefix('permissions')->group(function () {
        Route::get('/', [AdminPermissionController::class, 'index']);
        Route::post('/', [AdminPermissionController::class, 'store']);
        Route::get('/roles', [AdminPermissionController::class, 'roles']);
        Route::post('/roles', [AdminPermissionController::class, 'createRole']);
        Route::get('/{permission}', [AdminPermissionController::class, 'show']);
        Route::put('/{permission}', [AdminPermissionController::class, 'update']);
        Route::delete('/{permission}', [AdminPermissionController::class, 'destroy']);
    });

    // Feature management
    Route::prefix('features')->group(function () {
        Route::get('/', [AdminFeatureController::class, 'index']);
        Route::post('/', [AdminFeatureController::class, 'store']);
        Route::get('/analytics', [AdminFeatureController::class, 'analytics']);
        Route::put('/{feature}', [AdminFeatureController::class, 'update']);
        Route::put('/{feature}/toggle', [AdminFeatureController::class, 'toggle']);
        Route::delete('/{feature}', [AdminFeatureController::class, 'destroy']);
    });

    // Integration management
    Route::prefix('integrations')->group(function () {
        Route::get('/', [AdminIntegrationController::class, 'index']);
        Route::post('/', [AdminIntegrationController::class, 'store']);
        Route::get('/analytics', [AdminIntegrationController::class, 'analytics']);
        Route::post('/test', [AdminIntegrationController::class, 'test']);
        Route::put('/{integration}', [AdminIntegrationController::class, 'update']);
        Route::put('/{integration}/toggle', [AdminIntegrationController::class, 'toggle']);
        Route::delete('/{integration}', [AdminIntegrationController::class, 'destroy']);
    });

    // Location management
    Route::prefix('locations')->group(function () {
        Route::get('/', [AdminLocationController::class, 'index']);
        Route::post('/', [AdminLocationController::class, 'store']);
        Route::get('/analytics', [AdminLocationController::class, 'analytics']);
        Route::get('/{location}', [AdminLocationController::class, 'show']);
        Route::put('/{location}', [AdminLocationController::class, 'update']);
        Route::delete('/{location}', [AdminLocationController::class, 'destroy']);
    });

    // Zone management
    Route::prefix('zones')->group(function () {
        Route::get('/', [AdminZoneController::class, 'index']);
        Route::post('/', [AdminZoneController::class, 'store']);
        Route::get('/analytics', [AdminZoneController::class, 'analytics']);
        Route::get('/{zone}', [AdminZoneController::class, 'show']);
        Route::put('/{zone}', [AdminZoneController::class, 'update']);
        Route::delete('/{zone}', [AdminZoneController::class, 'destroy']);
    });

    // Address management
    Route::prefix('addresses')->group(function () {
        Route::get('/', [AdminAddressController::class, 'index']);
        Route::get('/analytics', [AdminAddressController::class, 'analytics']);
        Route::post('/bulk-operations', [AdminAddressController::class, 'bulkOperations']);
        Route::get('/{address}', [AdminAddressController::class, 'show']);
        Route::put('/{address}', [AdminAddressController::class, 'update']);
        Route::delete('/{address}', [AdminAddressController::class, 'destroy']);
    });

    // Collection management
    Route::prefix('collections')->group(function () {
        Route::get('/', [AdminCollectionController::class, 'index']);
        Route::post('/', [AdminCollectionController::class, 'store']);
        Route::get('/analytics', [AdminCollectionController::class, 'analytics']);
        Route::get('/{collection}', [AdminCollectionController::class, 'show']);
        Route::put('/{collection}', [AdminCollectionController::class, 'update']);
        Route::delete('/{collection}', [AdminCollectionController::class, 'destroy']);
    });

    // Content management
    Route::prefix('content')->group(function () {
        Route::get('/', [AdminContentController::class, 'index']);
        Route::post('/', [AdminContentController::class, 'store']);
        Route::get('/analytics', [AdminContentController::class, 'analytics']);
        Route::get('/{content}', [AdminContentController::class, 'show']);
        Route::put('/{content}', [AdminContentController::class, 'update']);
        Route::delete('/{content}', [AdminContentController::class, 'destroy']);
    });

    // Configuration management
    Route::prefix('config')->group(function () {
        Route::get('/', [AdminConfigController::class, 'index']);
        Route::put('/', [AdminConfigController::class, 'update']);
        Route::get('/groups', [AdminConfigController::class, 'groups']);
        Route::post('/groups', [AdminConfigController::class, 'createGroup']);
        Route::post('/reset', [AdminConfigController::class, 'reset']);
        Route::get('/export', [AdminConfigController::class, 'export']);
        Route::post('/import', [AdminConfigController::class, 'import']);
    });

    // API management
    Route::prefix('api')->group(function () {
        Route::get('/', [AdminApiController::class, 'index']);
        Route::post('/', [AdminApiController::class, 'store']);
        Route::get('/analytics', [AdminApiController::class, 'analytics']);
        Route::get('/logs', [AdminApiController::class, 'logs']);
        Route::get('/{key}', [AdminApiController::class, 'show']);
        Route::put('/{key}', [AdminApiController::class, 'update']);
        Route::delete('/{key}', [AdminApiController::class, 'destroy']);
    });

    // Audit management
    Route::prefix('audit')->group(function () {
        Route::get('/', [AdminAuditController::class, 'index']);
        Route::get('/analytics', [AdminAuditController::class, 'analytics']);
        Route::get('/critical', [AdminAuditController::class, 'critical']);
        Route::post('/export', [AdminAuditController::class, 'export']);
        Route::get('/{audit}', [AdminAuditController::class, 'show']);
    });
});
